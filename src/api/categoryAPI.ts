import API from "@/lib/axios";
import { AcademicYear, Bank, Category, CategoryFilter, CategoryFormData } from "@/types/category";
import { PaginatedResponse } from "@/types/courseRegistration";
import { AxiosError } from "axios";

export const listFacultyAPI = async (
  filters: CategoryFilter,
  page: number = 1,
  token: string | undefined
): Promise<PaginatedResponse<Category>> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: Category[];
      totalData: any;
    }>("/listFacultyAPI", { ...filters, page, token });

    if (response.data.code === 1 && response.data.data) {
      // Ánh xạ dữ liệu từ API của bạn sang cấu trúc PaginatedResponse
      return {
        data: response.data.data,
        totalData: response.data.totalData
      };
    } else {
      throw new Error(response.data.mess || "Không thể tải danh sách khoa.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // Nếu API trả về lỗi có cấu trúc { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // Lỗi chung từ Axios hoặc lỗi mạng
    throw new Error(
      error.message || "Lỗi không xác định khi lấy danh sách khoa."
    );
  }
};

export const listDeparmentAPI = async (
  filters: CategoryFilter,
  page: number = 1,
  token: string | undefined
): Promise<PaginatedResponse<Category>>=> {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: Category[];
      totalData: any;
    }>("/listDeparmentAPI", { ...filters, page, token });

    if (response.data.code === 1 && response.data.data) {
      // Ánh xạ dữ liệu từ API của bạn sang cấu trúc PaginatedResponse
      return {
        data: response.data.data,
        totalData: response.data.totalData,
      };
    } else {
      throw new Error(
        response.data.mess || "Không thể tải danh sách phòng ban."
      );
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // Nếu API trả về lỗi có cấu trúc { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // Lỗi chung từ Axios hoặc lỗi mạng
    throw new Error(
      error.message || "Lỗi không xác định khi lấy danh sách phòng ban."
    );
  }
};

export const listSubjectAPI = async (
  filters: CategoryFilter,
  page: number = 1,
  token: string | undefined
): Promise<PaginatedResponse<Category>> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: Category[];
      totalData: any;
    }>("/listSubjectAPI", { ...filters, page, token });

    if (response.data.code === 1 && response.data.data) {
      // Ánh xạ dữ liệu từ API của bạn sang cấu trúc PaginatedResponse
      return {
        data: response.data.data,
        totalData: response.data.totalData,
      };
    } else {
      throw new Error(response.data.mess || "Không thể tải danh sách môn học.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // Nếu API trả về lỗi có cấu trúc { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // Lỗi chung từ Axios hoặc lỗi mạng
    throw new Error(
      error.message || "Lỗi không xác định khi lấy danh sách môn học."
    );
  }
};

export const listMajorAPI = async (
  filters: CategoryFilter,
  page: number = 1,
  token: string | undefined
): Promise<PaginatedResponse<Category>> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: Category[];
      totalData: any;
    }>("/listMajorAPI", { ...filters, page, token });
    if (response.data.code === 1 && response.data.data) {
      return {
        data: response.data.data,
        totalData: response.data.totalData,
      };
    } else {
      throw new Error(
        response.data.mess || "Không thể tải danh sách ngành học."
      );
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // Nếu API trả về lỗi có cấu trúc { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // Lỗi chung từ Axios hoặc lỗi mạng
    throw new Error(error.message || "Lỗi không xác định khi đăng nhập");
  }
};

export const listAcademicYearsAPI = async (): Promise<AcademicYear[]> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: AcademicYear[];
      totalData: any;
    }>("/listAcademicYearsAPI");

    if (response.data.code === 1 && response.data.data) {
      // Ánh xạ dữ liệu từ API của bạn sang cấu trúc PaginatedResponse
      return response.data.data;
    } else {
      throw new Error(response.data.mess || "Không thể tải danh sách niên khóa.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // Nếu API trả về lỗi có cấu trúc { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // Lỗi chung từ Axios hoặc lỗi mạng
    throw new Error(
      error.message || "Lỗi không xác định khi lấy danh sách niên khóa."
    );
  }
};

export const detailCateAPI = async (
  accessToken: string,
  id: any
): Promise<Category> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: Category;
    }>("/getCateDetailAPI", {
      token: accessToken,
      id: id,
    });

    if (response.data.code === 0 && response.data.data) {
      return response.data.data;
    } else {
      throw new Error(response.data.mess || "Không thể tải thông tin chi tiết.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // Nếu API trả về lỗi có cấu trúc { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // Lỗi chung từ Axios hoặc lỗi mạng
    throw new Error(
      error.message || "Lỗi không xác định khi lấy thông tin chi tiết."
    );
  }
};

export const deleteCateAPI = async (
  id: any,
  accessToken: string
): Promise<{ code: number; mess: string; data: Category }> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: Category;
    }>("/deleteCateAPI", {
      token: accessToken,
      id: id,
    });

    if (response.data) {
      return response.data;
    } else {
      throw new Error("Không thể xóa.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // Nếu API trả về lỗi có cấu trúc { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // Lỗi chung từ Axios hoặc lỗi mạng
    throw new Error(
      error.message || "Lỗi không xác định khi xóa."
    );
  }
};

export const addFacultyAPI = async (
  faculty: CategoryFormData,
  token: string,
): Promise<{ code: number; mess: string; data: CategoryFormData }> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: CategoryFormData;
    }>("/addFacultyAPI", {...faculty, token });
    if (response.data.code === 0 && response.data.data) {
      return response.data;
    } else {
      throw new Error(response.data.mess || "Không thể thêm khoa.");
    }
  }
  catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;
   // Nếu API trả về lỗi có cấu trúc { code, mess }
   if (
    error.response &&
    error.response.data &&
    typeof error.response.data.mess === "string"
  ) {
    throw new Error(error.response.data.mess);
  }
  // Lỗi chung từ Axios hoặc lỗi mạng
  throw new Error(
    error.message || "Lỗi không xác định khi thêm mới khoa."
  );
  }
}

export const updateFacultyAPI = async (
  idUpdate: string,
  faculty: CategoryFormData,
  token: string,
): Promise<{ code: number; mess: string; data: CategoryFormData }> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: CategoryFormData;
    }>("/addFacultyAPI", {...faculty, token, id: idUpdate });
    if (response.data.code === 0 && response.data.data) {
      return response.data;
    } else {
      throw new Error(response.data.mess || "Không thể sửa khoa.");
    }
  }
  catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;
   // Nếu API trả về lỗi có cấu trúc { code, mess }
   if (
    error.response &&
    error.response.data &&
    typeof error.response.data.mess === "string"
  ) {
    throw new Error(error.response.data.mess);
  }
  // Lỗi chung từ Axios hoặc lỗi mạng
  throw new Error(
    error.message || "Lỗi không xác định khi sửa khoa."
  );
  }
}


export const addDeparmentAPI = async (
  Deparment: CategoryFormData,
  token: string,
): Promise<{ code: number; mess: string; data: CategoryFormData }> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: CategoryFormData;
    }>("/addDeparmentAPI", {...Deparment, token });
    if (response.data.code === 0 && response.data.data) {
      return response.data;
    } else {
      throw new Error(response.data.mess || "Không thể thêm phòng ban.");
    }
  }
  catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;
   // Nếu API trả về lỗi có cấu trúc { code, mess }
   if (
    error.response &&
    error.response.data &&
    typeof error.response.data.mess === "string"
  ) {
    throw new Error(error.response.data.mess);
  }
  // Lỗi chung từ Axios hoặc lỗi mạng
  throw new Error(
    error.message || "Lỗi không xác định khi thêm mới phòng ban."
  );
  }
}

export const updateDeparmentAPI = async (
  idUpdate: string,
  Deparment: CategoryFormData,
  token: string,
): Promise<{ code: number; mess: string; data: CategoryFormData }> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: CategoryFormData;
    }>("/addDeparmentAPI", {...Deparment, token, id: idUpdate });
    if (response.data.code === 0 && response.data.data) {
      return response.data;
    } else {
      throw new Error(response.data.mess || "Không thể chỉnh sửa phòng ban.");
    }
  }
  catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;
   // Nếu API trả về lỗi có cấu trúc { code, mess }
   if (
    error.response &&
    error.response.data &&
    typeof error.response.data.mess === "string"
  ) {
    throw new Error(error.response.data.mess);
  }
  // Lỗi chung từ Axios hoặc lỗi mạng
  throw new Error(
    error.message || "Lỗi không xác định khi chỉnh sửa phòng ban."
  );
  }
}

export const addMajorAPI = async (
  major: CategoryFormData,
  token: string,
): Promise<{ code: number; mess: string; data: CategoryFormData }> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: CategoryFormData;
    }>("/addMajorAPI", {...major, token });
    if (response.data.code === 0 && response.data.data) {
      return response.data;
    } else {
      throw new Error(response.data.mess || "Không thể thêm ngành học.");
    }
  }
  catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;
   // Nếu API trả về lỗi có cấu trúc { code, mess }
   if (
    error.response &&
    error.response.data &&
    typeof error.response.data.mess === "string"
  ) {
    throw new Error(error.response.data.mess);
  }
  // Lỗi chung từ Axios hoặc lỗi mạng
  throw new Error(
    error.message || "Lỗi không xác định khi thêm mới ngành học."
  );
  }
}

export const updateMajorAPI = async (
  idUpdate: string,
  major: CategoryFormData,
  token: string,
): Promise<{ code: number; mess: string; data: CategoryFormData }> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: CategoryFormData;
    }>("/addMajorAPI", {...major, token, id: idUpdate });
    if (response.data.code === 0 && response.data.data) {
      return response.data;
    } else {
      throw new Error(response.data.mess || "Không thể chỉnh sửa ngành học.");
    }
  }
  catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;
   // Nếu API trả về lỗi có cấu trúc { code, mess }
   if (
    error.response &&
    error.response.data &&
    typeof error.response.data.mess === "string"
  ) {
    throw new Error(error.response.data.mess);
  }
  // Lỗi chung từ Axios hoặc lỗi mạng
  throw new Error(
    error.message || "Lỗi không xác định khi chỉnh sửa ngành học."
  );
  }
}

export const addSubjectAPI = async (
  subject: CategoryFormData,
  token: string,
): Promise<{ code: number; mess: string; data: CategoryFormData }> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: CategoryFormData;
    }>("/addSubjectAPI", {...subject, token });
    if (response.data.code === 0 && response.data.data) {
      return response.data;
    } else {
      throw new Error(response.data.mess || "Không thể thêm môn học.");
    }
  }
  catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;
   // Nếu API trả về lỗi có cấu trúc { code, mess }
   if (
    error.response &&
    error.response.data &&
    typeof error.response.data.mess === "string"
  ) {
    throw new Error(error.response.data.mess);
  }
  // Lỗi chung từ Axios hoặc lỗi mạng
  throw new Error(
    error.message || "Lỗi không xác định khi thêm mới môn học."
  );
  }
}

export const updateSubjectAPI = async (
  idUpdate: string,
  subject: CategoryFormData,
  token: string,
): Promise<{ code: number; mess: string; data: CategoryFormData }> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: CategoryFormData;
    }>("/addSubjectAPI", {...subject, token, id: idUpdate });
    if (response.data.code === 0 && response.data.data) {
      return response.data;
    } else {
      throw new Error(response.data.mess || "Không thể chỉnh sửa môn học.");
    }
  }
  catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;
   // Nếu API trả về lỗi có cấu trúc { code, mess }
   if (
    error.response &&
    error.response.data &&
    typeof error.response.data.mess === "string"
  ) {
    throw new Error(error.response.data.mess);
  }
  // Lỗi chung từ Axios hoặc lỗi mạng
  throw new Error(
    error.message || "Lỗi không xác định khi chỉnh sửa môn học."
  );
  }
}

export const getListBank = async () => {
  try {
    const response = await API.post("/getLinstBank");
    if (response.data) {
      return response.data;
    } else {
      throw new Error(response.data.mess || "Không thể tải danh sách ngân hàng.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;
    // Nếu API trả về lỗi có cấu trúc { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
  }
}

