import API from "@/lib/axios";
import { PaginatedResponse } from "@/types/courseRegistration";
import { ClassFormData, ClassRoom } from "@/types/managerclass";
import { AxiosError } from "axios";

export const listClasseAPI = async (
  filters:  any,
  page: number = 1,
  token: string | undefined,
  id_member: number,
  limit?: number
): Promise<PaginatedResponse<ClassRoom>> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: ClassRoom[];
      totalData: any;
    }>("/listClasseAPI", { ...filters, page, token, id_member, limit });

    if (response.data.code === 1 && response.data.data) {
      // Ánh xạ dữ liệu từ API của bạn sang cấu trúc PaginatedResponse
      return {
        data: response.data.data,
        totalData: response.data.totalData?.totalData,
      };
    } else {
      throw new Error(
        response.data.mess || "Không thể tải danh sách lớp học"
      );
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // Nếu API trả về lỗi có cấu trúc { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // Lỗi chung từ Axios hoặc lỗi mạng
    throw new Error(
      error.message || "Lỗi không xác định khi lấy danh sách lớp học"
    );
  }
};

export const detailClasseAPI = async (
  accessToken: string,
  id: any
): Promise<ClassRoom> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: ClassRoom;
    }>("/detailClasseAPI", {
      token: accessToken,
      id: id,
    });

    if (response.data.code === 0 && response.data.data) {
      return response.data.data;
    } else {
      throw new Error(response.data.mess || "Không thể tải chi tiết lớp học.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // Nếu API trả về lỗi có cấu trúc { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // Lỗi chung từ Axios hoặc lỗi mạng
    throw new Error(
      error.message || "Lỗi không xác định khi lấy chi tiết học phần."
    );
  }
};


export const listStudentsInClassAPI = async (
  filters:  any,
  page: number = 1,
  token: string | undefined,
  id_class: string,
  limit?: number,
): Promise<{
  data: any;
  infoClass: any;
  totalData: any;
}> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: any;
      infoClass : any,
      totalData: any;
    }>("/listStudentsInClassAPI", { student_name: filters.student_name || filters.student_code, page, token, id_class, limit });

    if (response.data.code === 1 && response.data.data) {
      // Ánh xạ dữ liệu từ API của bạn sang cấu trúc PaginatedResponse
      return {
        data: response.data.data?.students,
        infoClass: response.data.data?.class_info,
        totalData: response.data.totalData?.totalData,
      };
    } else {
      throw new Error(
        response.data.mess || "Không thể tải danh sách lớp học"
      );
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // Nếu API trả về lỗi có cấu trúc { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // Lỗi chung từ Axios hoặc lỗi mạng
    throw new Error(
      error.message || "Lỗi không xác định khi lấy danh sách lớp học"
    );
  }
};
