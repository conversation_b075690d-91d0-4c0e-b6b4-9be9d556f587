import API from "@/lib/axios";
import { PaginatedResponse } from "@/types/courseRegistration";
import { Course, CourseFilter, CourseFormData } from "@/types/managerCourse";
import { AxiosError } from "axios";

export const listCourceAPI = async (
  filters: CourseFilter,
  page: number = 1,
  token: string | undefined
): Promise<PaginatedResponse<Course>> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: Course[];
      totalData: any;
    }>("/listCourseAPI", { ...filters, page, token });

    if (response.data.code === 1 && response.data.data) {
      // Ánh xạ dữ liệu từ API của bạn sang cấu trúc PaginatedResponse
      return {
        data: response.data.data,
        totalData: response.data.totalData?.totalData,
      };
    } else {
      throw new Error(
        response.data.mess || "Không thể tải danh sách học phần."
      );
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // Nếu API trả về lỗi có cấu trúc { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // Lỗi chung từ Axios hoặc lỗi mạng
    throw new Error(
      error.message || "Lỗi không xác định khi lấy danh sách học phần."
    );
  }
};

export const detailCourseAPI = async (
  accessToken: string,
  id: any
): Promise<Course> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: Course;
    }>("/detailCourseAPI", {
      token: accessToken,
      id: id,
    });

    if (response.data.code === 0 && response.data.data) {
      return response.data.data;
    } else {
      throw new Error(response.data.mess || "Không thể tải chi tiết học phần.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // Nếu API trả về lỗi có cấu trúc { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // Lỗi chung từ Axios hoặc lỗi mạng
    throw new Error(
      error.message || "Lỗi không xác định khi lấy chi tiết học phần."
    );
  }
};

export const updateCourseAPI = async (
  accessToken: string,
  data: Course
): Promise<{ code: number; mess: string; data: Course }> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: Course;
    }>("/addCourseAPI", {
      token: accessToken,
      ...data
    });

    if (response.data.code === 0 && response.data.data) {
      return response.data;
    } else {
      throw new Error(response.data.mess || "Không thể chỉnh sửa học phần.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // Nếu API trả về lỗi có cấu trúc { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // Lỗi chung từ Axios hoặc lỗi mạng
    throw new Error(
      error.message || "Lỗi không xác định khi chỉnh sửa học phần."
    );
  }
};

export const addCourseAPI = async (
  accessToken: string,
  data: CourseFormData
): Promise<{ code: number; mess: string; data: CourseFormData }> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: CourseFormData;
    }>("/addCourseAPI", {
      token: accessToken,
      ...data,
      schedule_session: [],
      id_teacher: null
    });

    if (response.data.code === 0 && response.data.data) {
      return response.data;
    } else {
      throw new Error(response.data.mess || "Không thể thêm mới học phần.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // Nếu API trả về lỗi có cấu trúc { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // Lỗi chung từ Axios hoặc lỗi mạng
    throw new Error(
      error.message || "Lỗi không xác định khi thêm mới học phần."
    );
  }
};

export const deleteCourseAPI = async (
  accessToken: string,
  id: any
): Promise<{ code: number; mess: string; data: Course }> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: Course;
    }>("/deleteCourseAPI", {
      token: accessToken,
      id: id,
    });

    if (response.data) {
      return response.data;
    } else {
      throw new Error("Không thể xóa học phần.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // Nếu API trả về lỗi có cấu trúc { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // Lỗi chung từ Axios hoặc lỗi mạng
    throw new Error(
      error.message || "Lỗi không xác định khi xóa học phần."
    );
  }
};


