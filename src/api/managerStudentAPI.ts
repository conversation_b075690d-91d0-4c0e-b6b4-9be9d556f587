import API from "@/lib/axios";
import { PaginatedResponse } from "@/types/courseRegistration";
import {
  InfoFileStudentData,
  InfoFileStudentFilters,
  Student,
  StudentFilters,
  StudentFormData,
} from "@/types/managerStudent";
import { AxiosError } from "axios";

export const listStudentAPI = async (
  filters: StudentFilters,
  page: number = 1,
  token: string | undefined,
  limit?: number
): Promise<PaginatedResponse<Student>> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: Student[];
      totalData: number;
    }>("/listStudentAPI", { ...filters, page, token, limit   });

    if (response.data.code === 1 && response.data.data) {
      // Ánh xạ dữ liệu từ API của bạn sang cấu trúc PaginatedResponse
      return {
        data: response.data.data,
        totalData: response.data.totalData,
      };
    } else {
      throw new Error(response.data.mess || "<PERSON>hông thể tải danh sách học viên.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // Nếu API trả về lỗi có cấu trúc { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // Lỗi chung từ Axios hoặc lỗi mạng
    throw new Error(
      error.message || "Lỗi không xác định khi lấy danh sách học viên."
    );
  }
};

export const detailStudentAPI = async (
  id: any,
  accessToken: string
): Promise<Student> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: Student;
    }>("/detailStudentAPI", {
      token: accessToken,
      id: id,
    });

    if (response.data.code === 1 && response.data.data) {
      return response.data.data;
    } else {
      throw new Error(response.data.mess || "Không thể tải chi tiết Học viên.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // Nếu API trả về lỗi có cấu trúc { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // Lỗi chung từ Axios hoặc lỗi mạng
    throw new Error(
      error.message || "Lỗi không xác định khi lấy chi tiết Học viên."
    );
  }
};

export const updateStudentAPI = async (
  editingRegistrationId: string | null,
  data: StudentFormData,
  accessToken: string
): Promise<{ code: number; mess: string; data: Student }> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: Student;
    }>("/addStudentAPI", {
      // Assuming the endpoint for adding is /addRegisterStudyAPI
      ...data,
      id: editingRegistrationId,
      token: accessToken,
    });

    if (response.data.code === 1) {
      return response.data;
    } else {
      throw new Error(response.data.mess || "Không thể sửa học viên.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi sửa học viên.");
  }
};

export const listInfoFileStudentAPI = async (
  filters: InfoFileStudentFilters,
  page: number = 1,
  token: string | undefined,
  idStudent: string
): Promise<PaginatedResponse<InfoFileStudentData>> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: InfoFileStudentData[];
      totalData: number;
    }>("/listInfoFileStudentAPI", {
      ...filters,
      page,
      token,
      id_student: idStudent,
    });

    if (response.data.code === 1 && response.data.data) {
      // Ánh xạ dữ liệu từ API của bạn sang cấu trúc PaginatedResponse
      return {
        data: response.data.data,
        totalData: response.data.totalData,
      };
    } else {
      throw new Error(response.data.mess || "Không thể tải danh sách đăng ký.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // Nếu API trả về lỗi có cấu trúc { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // Lỗi chung từ Axios hoặc lỗi mạng
    throw new Error(
      error.message || "Lỗi không xác định khi lấy danh sách đăng ký."
    );
  }
};

export const detailInfoFileStudentAPI = async (
  id: any,
  accessToken: string
): Promise<InfoFileStudentData> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: InfoFileStudentData;
    }>("/detailInfoFileStudentAPI", {
      token: accessToken,
      id: id,
    });

    if (response.data.code === 1 && response.data.data) {
      return response.data.data;
    } else {
      throw new Error(response.data.mess || "Không thể tải chi tiết đăng ký.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // Nếu API trả về lỗi có cấu trúc { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // Lỗi chung từ Axios hoặc lỗi mạng
    throw new Error(
      error.message || "Lỗi không xác định khi lấy chi tiết đăng ký."
    );
  }
};

export const updateInfoFileStudentAPI = async (
  data: FormData
): Promise<{ code: number; mess: string; data: InfoFileStudentData }> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: InfoFileStudentData;
    }>("/addInfoFileStudentAPI", data);

    if (response.data.code === 1) {
      return response.data;
    } else {
      throw new Error(response.data.mess || "Không thể sửa tài liệu.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi sửa tài liệu.");
  }
};

export const addInfoFileStudentAPI = async (
  data: FormData
): Promise<{ code: number; mess: string; data: InfoFileStudentData }> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: InfoFileStudentData;
    }>("/addInfoFileStudentAPI", data);

    if (response.data.code === 1) {
      return response.data;
    } else {
      throw new Error(response.data.mess || "Không thể thêm tài liệu.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi thêm tài liệu.");
  }
};

export const deleteInfoFileStudentAPI = async (
  id: string,
  accessToken: string
): Promise<{ code: number; mess: string; data: InfoFileStudentData }> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: InfoFileStudentData; 
    }>("/deleteInfoFileStudentAPI", {
      token: accessToken,
      id: id,
    });

    if (response.data.code === 1) {
      return response.data;
    } else {
      throw new Error(response.data.mess || "Không thể xóa tài liệu.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi xóa tài liệu.");
  }
}
