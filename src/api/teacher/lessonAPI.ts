import API from "@/lib/axios";
import { AxiosError } from "axios";
import {
  Lesson,
  LessonApiResponse,
  PaginatedLessonResponse,
  ListLessonsRequest,
  LessonDetailRequest,
  AddLessonRequest,
  DeleteLessonRequest,
  LessonFormData,
} from "@/types/lesson";

// List lessons API
export const listLessonsAPI = async (
  filters: Partial<ListLessonsRequest>,
  page: number = 1,
  token: string,
  limit: number = 20
): Promise<PaginatedLessonResponse> => {
  try {
    const requestData = {
      token,
      page,
      limit,
      ...filters,
    };

    const response = await API.post<LessonApiResponse<Lesson[]>>(
      "/listLessonsAPI",
      requestData
    );

    if (response.data.code === 1 && response.data.data) {
      return {
        data: response.data.data,
        totalData: response.data.totalData?.totalData || 0,
        totalPage: response.data.totalData?.totalPage || 1,
      };
    } else {
      throw new Error(response.data.mess || "Không thể tải danh sách bài học.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi tải danh sách bài học.");
  }
};

// Get lesson detail API
export const detailLessonAPI = async (
  token: string,
  id: string
): Promise<Lesson> => {
  try {
    const response = await API.post<LessonApiResponse<Lesson>>(
      "/detailLessonlAPI",
      {
        token,
        id,
      }
    );

    if (response.data.code === 1 && response.data.data) {
      return response.data.data;
    } else {
      throw new Error(response.data.mess || "Không thể tải chi tiết bài học.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi tải chi tiết bài học.");
  }
};

// Add/Edit lesson API
export const addLessonAPI = async (
  data: LessonFormData,
  token: string,
  isEdit: boolean = false,
  lessonId?: string
): Promise<{ code: number; mess: string; data: Lesson }> => {
  try {
    const formData = new FormData();
    
    // Add required fields
    formData.append("token", token);
    formData.append("title", data.title);
    formData.append("content", data.content);
    formData.append("id_course", data.id_course);
    formData.append("status", data.status);

    // Add optional fields
    if (data.description) {
      formData.append("description", data.description);
    }

    // Always add youtube_code (empty string if not provided)
    formData.append("youtube_code", data.youtube_code || "");

    // Always add link_file_video
    if (data.link_file_video instanceof File) {
      formData.append("link_file_video", data.link_file_video);
    } else {
      // Send as empty string if no file
      formData.append("link_file_video", data.link_file_video || "");
    }

    // Add lesson ID for edit operations
    if (isEdit && lessonId) {
      formData.append("id", lessonId);
    }


    const response = await API.post<{
      code: number;
      mess: string;
      data: Lesson;
    }>("/addLessonAPI", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });

    if (response.data.code === 1) {
      return response.data;
    } else {
      throw new Error(
        response.data.mess || 
        (isEdit ? "Không thể cập nhật bài học." : "Không thể thêm bài học.")
      );
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(
      error.message || 
      (isEdit 
        ? "Lỗi không xác định khi cập nhật bài học." 
        : "Lỗi không xác định khi thêm bài học.")
    );
  }
};

// Delete lesson API
export const deleteLessonAPI = async (
  id: string,
  token: string
): Promise<{ code: number; mess: string; data: any }> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: any;
    }>("/deleteLessonAPI", {
      token,
      id,
    });

    if (response.data.code === 0) {
      return response.data;
    } else {
      throw new Error(response.data.mess || "Không thể xóa bài học.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi xóa bài học.");
  }
};

// Utility function to validate video file
// Utility function to validate video file
export const validateVideoFile = (file: any): string | null => {
  // Kiểm tra xem có đang ở môi trường trình duyệt không
  if (typeof window === 'undefined') {
    return null; // Trên server, bỏ qua validation
  }
  
  const maxSize = 100 * 1024 * 1024; // 100MB
  const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv'];
  
  if (file.size > maxSize) {
    return "Kích thước file không được vượt quá 100MB";
  }
  
  if (!allowedTypes.includes(file.type)) {
    return "Chỉ chấp nhận các định dạng video: MP4, AVI, MOV, WMV, FLV";
  }
  
  return null;
};

// Utility function to format file size
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Utility function to extract YouTube video ID from URL
export const extractYouTubeVideoId = (url: string): string | null => {
  const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
  const match = url.match(regex);
  return match ? match[1] : null;
};
