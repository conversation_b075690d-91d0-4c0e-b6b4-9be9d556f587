import API from "@/lib/axios";
import { PaginatedResponse } from "@/types/courseRegistration";
import { DocumentFilter, DocumentItem } from "@/types/managerFile";
import { AxiosError } from "axios";

export const listDocumentSubjectFileAPI = async (
  filters: DocumentFilter,
  page: number = 1,
  token: string | undefined,
): Promise<PaginatedResponse<DocumentItem>> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: DocumentItem[];
      totalData: any;
    }>("/listDocumentSubjectFileAPI", {
      ...filters,
      page,
      token,
    });

    if (response.data.code === 1 && response.data.data) {
      // Ánh xạ dữ liệu từ API của bạn sang cấu trúc PaginatedResponse
      return {
        data: response.data.data,
        totalData: response.data.totalData?.totalData,
      };
    } else {
      throw new Error(response.data.mess || "<PERSON>hông thể tải danh sách tài liệu.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // Nếu API trả về lỗi có cấu trúc { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // Lỗi chung từ Axios hoặc lỗi mạng
    throw new Error(
      error.message || "Lỗi không xác định khi lấy danh sách tài liệu."
    );
  }
};

export const detailDocumentSubjectFileAPI = async (
  id: any,
  accessToken: string
): Promise<DocumentItem> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: DocumentItem;
    }>("/detailDocumentSubjectFileAPI", {
      token: accessToken,
      id: id,
    });

    if (response.data.code === 1 && response.data.data) {
      return response.data.data;
    } else {
      throw new Error(response.data.mess || "Không thể tải chi tiết tài liệu.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // Nếu API trả về lỗi có cấu trúc { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // Lỗi chung từ Axios hoặc lỗi mạng
    throw new Error(
      error.message || "Lỗi không xác định khi lấy chi tiết tài liệu."
    );
  }
};

export const updateDocumentSubjectFileAPI = async (
  data: FormData
): Promise<{ code: number; mess: string; data: DocumentItem }> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: DocumentItem;
    }>("/addDocumentSubjectFileAPI", data);

    if (response.data.code === 1) {
      return response.data;
    } else {
      throw new Error(response.data.mess || "Không thể sửa tài liệu.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi sửa tài liệu.");
  }
};

export const addDocumentSubjectFileAPI = async (
  data: FormData
): Promise<{ code: number; mess: string; data: DocumentItem }> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: DocumentItem;
    }>("/addDocumentSubjectFileAPI", data);

    if (response.data.code === 1) {
      return response.data;
    } else {
      throw new Error(response.data.mess || "Không thể thêm tài liệu.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi thêm tài liệu.");
  }
};

export const deleteDocumentFileAPI = async (
  id: string,
  accessToken: string
): Promise<{ code: number; mess: string; data: DocumentItem }> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: DocumentItem; 
    }>("/deleteDocumentFileAPI", {
      token: accessToken,
      id: id,
    });

    if (response.data.code === 1) {
      return response.data;
    } else {
      throw new Error(response.data.mess || "Không thể xóa tài liệu.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi xóa tài liệu.");
  }
}