import API from "@/lib/axios";
import { ApiResponse, CourseData } from "@/types/teacherGrade";
import { AxiosError } from "axios";

export const viewGradeSheetAPI = async (
  filters: any,
  page: number = 1,
  token: string | undefined,
  id_course: any
): Promise<ApiResponse<CourseData>> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: CourseData;
      totalData: any;
    }>("/viewGradeSheetAPI", {
      searchStudent: filters,
      page,
      token,
      id_course,
    });

    if (response.data.code === 0 && response.data.data) {
      // Ánh xạ dữ liệu từ API của bạn sang cấu trúc PaginatedResponse
      return {
        data: response.data.data,
        totalData: response.data.totalData?.totalData,
      };
    } else {
      throw new Error(response.data.mess || "Không thể tải danh sách điểm.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // Nếu API trả về lỗi có cấu trúc { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // Lỗi chung từ Axios hoặc lỗi mạng
    throw new Error(
      error.message || "Lỗi không xác định khi lấy danh sách điểm."
    );
  }
};

export const updateGradeSheetAPI = async (
  accessToken: string,
  data: any,
  idGrade: any
): Promise<{ code: number; mess: string; data: any }> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: any;
    }>("/addMultipleGradesAPI", {
      token: accessToken,
      id_course: idGrade,
      grades: data,
    });

    if (response.data.code === 0 && response.data.data) {
      return response.data;
    } else {
      throw new Error(
        response.data.mess || "Không thể chỉnh sửa danh sách điểm."
      );
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // Nếu API trả về lỗi có cấu trúc { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // Lỗi chung từ Axios hoặc lỗi mạng
    throw new Error(
      error.message || "Lỗi không xác định khi chỉnh sửa danh sách điểm."
    );
  }
};

export const publishGradeAPI = async (
  accessToken: string,
  idGrade: any,
  gradeType: any
): Promise<{ code: number; mess: string; data: any }> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: any;
    }>("/publishGradeAPI", {
      token: accessToken,
      id_course: idGrade,
      grade_type: gradeType,
    });

    if (response.data.code === 0 && response.data.data) {
      return response.data;
    } else {
      throw new Error(response.data.mess || "Không thể công bố điểm.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    // Nếu API trả về lỗi có cấu trúc { code, mess }
    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    // Lỗi chung từ Axios hoặc lỗi mạng
    throw new Error(error.message || "Lỗi không xác định khi công bố điểm.");
  }
};
