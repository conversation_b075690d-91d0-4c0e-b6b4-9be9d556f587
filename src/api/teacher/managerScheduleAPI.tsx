import API from "@/lib/axios";
import { PaginatedResponse } from "@/types/courseRegistration";
import { AxiosError } from "axios";

export const listScheduleAPI = async (
    token: string | undefined,
    week_start?: string,
    week_end?: string
  ): Promise<PaginatedResponse<any>> => {
    try {
      const response = await API.post<{
        code: number;
        mess: string;
        data: any[];
        totalData: any;
      }>("/listCourseScheduleForTeacherAPI", { token, week_start, week_end });
  
      if (response.data.code === 0 && response.data.data) {
        // Ánh xạ dữ liệu từ API của bạn sang cấu trúc PaginatedResponse
        return {
          data: response.data.data,
          totalData: response.data.totalData?.totalData,
        };
      } else {
        throw new Error(
          response.data.mess || "Không thể tải danh sách học phần."
        );
      }
    } catch (err) {
      const error = err as AxiosError<{
        code?: number;
        mess?: string;
        data?: any;
      }>;
  
      // Nếu API trả về lỗi có cấu trúc { code, mess }
      if (
        error.response &&
        error.response.data &&
        typeof error.response.data.mess === "string"
      ) {
        throw new Error(error.response.data.mess);
      }
      // Lỗi chung từ Axios hoặc lỗi mạng
      throw new Error(
        error.message || "Lỗi không xác định khi lấy danh sách học phần."
      );
    }
  };