import API from "@/lib/axios";
import { AxiosError } from "axios";
import {
  Question,
  QuestionApiResponse,
  PaginatedQuestionResponse,
  ListQuestionsRequest,
  QuestionDetailRequest,
  AddQuestionRequest,
  DeleteQuestionRequest,
  QuestionFormData,
} from "@/types/question";

// List questions API
export const listQuestionsAPI = async (
  filters: Partial<ListQuestionsRequest>,
  page: number = 1,
  token: string,
  limit: number = 20
): Promise<PaginatedQuestionResponse> => {
  try {
    const requestData = {
      token,
      page,
      limit,
      ...filters,
    };

    const response = await API.post<QuestionApiResponse<Question[]>>(
      "/listQuestionsAPI",
      requestData
    );

    if (response.data.code === 0 && response.data.data) {
      return {
        data: response.data.data,
        totalData: response.data.totalData?.totalData || 0,
        totalPage: response.data.totalData?.totalPage || 1,
      };
    } else {
      throw new Error(response.data.mess || "Không thể tải danh sách câu hỏi.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi tải danh sách câu hỏi.");
  }
};

// Get question detail API
export const detailQuestionAPI = async (
  token: string,
  id: string
): Promise<Question> => {
  try {
    const response = await API.post<QuestionApiResponse<Question>>(
      "/detailQuestionAPI",
      {
        token,
        id,
      }
    );

    if (response.data.code === 0 && response.data.data) {
      return response.data.data;
    } else {
      throw new Error(response.data.mess || "Không thể tải chi tiết câu hỏi.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi tải chi tiết câu hỏi.");
  }
};

// Add/Edit question API
export const addQuestionAPI = async (
  data: QuestionFormData,
  token: string,
  isEdit: boolean = false,
  questionId?: string
): Promise<{ code: number; mess: string; data: Question }> => {
  try {
    const requestData: AddQuestionRequest = {
      token,
      question: data.question,
      option_a: data.option_a,
      option_b: data.option_b,
      option_c: data.option_c,
      option_d: data.option_d,
      option_true: data.option_true,
      status: data.status,
    };

    // Add ID for editing
    if (isEdit && questionId) {
      requestData.id = questionId;
    }

    const response = await API.post<{
      code: number;
      mess: string;
      data: Question;
    }>("/addQuestionAPI", {
      ...requestData,
    });

    if (response.data.code === 0 && response.data.data) {
      return response.data;
    } else {
      throw new Error(
        response.data.mess || 
        (isEdit ? "Không thể cập nhật câu hỏi." : "Không thể thêm câu hỏi.")
      );
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(
      error.message || 
      (isEdit 
        ? "Lỗi không xác định khi cập nhật câu hỏi." 
        : "Lỗi không xác định khi thêm câu hỏi.")
    );
  }
};

// Delete question API
export const deleteQuestionAPI = async (
  token: string,
  id: number,
  forceDelete: boolean = true
): Promise<{ code: number; mess: string }> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
    }>(
      "/deleteQuestionAPI",
      {
        token,
        id,
        force_delete: forceDelete
      }
    );

    if (response.data.code === 0) {
      return response.data;
    } else {
      throw new Error(response.data.mess || "Không thể xóa câu hỏi.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi xóa câu hỏi.");
  }
};
