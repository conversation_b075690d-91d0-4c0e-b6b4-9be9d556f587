import API from "@/lib/axios";
import { AxiosError } from "axios";
import {
  Test,
  TestApiResponse,
  PaginatedTestResponse,
  ListTestsRequest,
  AddTestRequest,
  TestFormData,
} from "@/types/test";

// List tests API
export const listTestsAPI = async (
  filters: Partial<ListTestsRequest>,
  page: number = 1,
  token: string,
  limit: number = 20
): Promise<PaginatedTestResponse> => {
  try {
    const requestData = {
      token,
      page,
      limit,
      ...filters,
    };

    const response = await API.post<TestApiResponse<Test[]>>(
      "/listTestsAPI",
      requestData
    );

    if (response.data.code === 1 && response.data.data) {
      return {
        data: response.data.data,
        totalData: response.data.totalData?.totalData || 0,
        totalPage: response.data.totalData?.totalPage || 1,
      };
    } else {
      throw new Error(response.data.mess || "Không thể tải danh sách bài kiểm tra.");
    }
  } catch (err) {
    const error = err as AxiosError<{
      code?: number;
      mess?: string;
      data?: any;
    }>;

    if (
      error.response &&
      error.response.data &&
      typeof error.response.data.mess === "string"
    ) {
      throw new Error(error.response.data.mess);
    }
    throw new Error(error.message || "Lỗi không xác định khi tải danh sách bài kiểm tra.");
  }
};

// Get test detail API
export const detailTestAPI = async (
  token: string,
  id: string
): Promise<Test> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data: {
        test: {
          id: number;
          title: string;
          description?: string;
          id_lesson: number;
          time_test: number;
          time_start: number;
          time_end: number;
          status: string;
          point_min: number;
          id_question?: string;
        };
        questions: Array<{
          id: number;
          question: string;
          option_a: string;
          option_b: string;
          option_c: string;
          option_d: string;
          option_true: string;
          status: string;
        }>;
        total_questions: number;
      };
    }>("/detailTestAPI", {
      token,
      id,
    });

    if (response.data.code === 0 && response.data.data) {
      const testData = response.data.data.test;
      const questionsData = response.data.data.questions || [];

      // Convert Unix timestamps to datetime strings
      const formatUnixToDateTime = (timestamp: number): string => {
        const date = new Date(timestamp * 1000);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      };

      // Map questions to TestQuestion format
      const mappedQuestions = questionsData.map((q) => ({
        question: q.question,
        option_a: q.option_a,
        option_b: q.option_b,
        option_c: q.option_c,
        option_d: q.option_d,
        option_true: q.option_true as 'A' | 'B' | 'C' | 'D',
      }));

      // Return mapped test data
      return {
        id: testData.id.toString(),
        title: testData.title,
        description: testData.description || "",
        id_lesson: testData.id_lesson.toString(),
        time_test: testData.time_test,
        time_start: formatUnixToDateTime(testData.time_start),
        time_end: formatUnixToDateTime(testData.time_end),
        status: testData.status as 'active' | 'inactive',
        point_min: testData.point_min,
        questions: mappedQuestions,
      };
    } else {
      throw new Error(response.data.mess || "Không thể tải chi tiết bài kiểm tra.");
    }
  } catch (err) {
    const error = err as Error;
    throw new Error(error.message || "Lỗi không xác định khi tải chi tiết bài kiểm tra.");
  }
};

// Add/Edit test API
export const addTestAPI = async (
  data: TestFormData,
  token: string,
  isEdit: boolean = false,
  testId?: string
): Promise<{ code: number; mess: string; data: Test }> => {
  try {
    const requestData: AddTestRequest = {
      token,
      title: data.title,
      description: data.description,
      id_lesson: data.id_lesson,
      time_test: data.time_test,
      time_start: data.time_start,
      time_end: data.time_end,
      status: data.status,
      point_min: data.point_min,
      questions: data.questions,
    };

    // Add ID for editing
    if (isEdit && testId) {
      requestData.id = testId;
    }

    const response = await API.post<{
      code: number;
      mess: string;
      data: Test;
    }>("/addTestAPI", {
      ...requestData,
    });

    if (response.data.code === 0 && response.data.data) {
      return response.data;
    } else {
      throw new Error(
        response.data.mess || 
        (isEdit ? "Không thể cập nhật bài kiểm tra." : "Không thể thêm bài kiểm tra.")
      );
    }
  } catch (err) {
    const error = err as Error;
    throw new Error(
      error.message || 
      (isEdit 
        ? "Lỗi không xác định khi cập nhật bài kiểm tra." 
        : "Lỗi không xác định khi thêm bài kiểm tra.")
    );
  }
};

// Delete test API
export const deleteTestAPI = async (
  token: string,
  id: string
): Promise<{ code: number; mess: string }> => {
  try {
    const response = await API.post<{ code: number; mess: string }>(
      "/deleteTestAPI",
      {
        token,
        id,
      }
    );

    if (response.data.code === 0) {
      return response.data;
    } else {
      throw new Error(response.data.mess || "Không thể xóa bài kiểm tra.");
    }
  } catch (err) {
    const error = err as Error;
    throw new Error(error.message || "Lỗi không xác định khi xóa bài kiểm tra.");
  }
};

// Helper function to format datetime for API
export const formatDateTimeForAPI = (dateTime: Date): string => {
  const year = dateTime.getFullYear();
  const month = String(dateTime.getMonth() + 1).padStart(2, '0');
  const day = String(dateTime.getDate()).padStart(2, '0');
  const hours = String(dateTime.getHours()).padStart(2, '0');
  const minutes = String(dateTime.getMinutes()).padStart(2, '0');
  const seconds = String(dateTime.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// Helper function to parse datetime from API
export const parseDateTimeFromAPI = (dateTimeString: string): Date => {
  // Handle format: "2025-08-01 08:00:00"
  const [datePart, timePart] = dateTimeString.split(' ');
  const [year, month, day] = datePart.split('-').map(Number);
  const [hours, minutes, seconds] = timePart.split(':').map(Number);

  return new Date(year, month - 1, day, hours, minutes, seconds);
};

// Helper function to convert datetime string to datetime-local format for form inputs
export const formatDateTimeForForm = (dateTimeString: string): string => {
  try {
    const date = parseDateTimeFromAPI(dateTimeString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  } catch {
    return "";
  }
};

// Helper function to validate test dates
export const validateTestDates = (startDate: Date, endDate: Date): string | null => {
  const now = new Date();
  
  if (startDate < now) {
    return "Thời gian bắt đầu không thể trong quá khứ";
  }
  
  if (endDate <= startDate) {
    return "Thời gian kết thúc phải sau thời gian bắt đầu";
  }
  
  const timeDiff = endDate.getTime() - startDate.getTime();
  const hoursDiff = timeDiff / (1000 * 60 * 60);
  
  if (hoursDiff > 24) {
    return "Thời gian kiểm tra không được vượt quá 24 giờ";
  }
  
  return null;
};

// Helper function to format duration
export const formatDuration = (minutes: number): string => {
  if (minutes < 60) {
    return `${minutes} phút`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (remainingMinutes === 0) {
    return `${hours} giờ`;
  }
  
  return `${hours} giờ ${remainingMinutes} phút`;
};
