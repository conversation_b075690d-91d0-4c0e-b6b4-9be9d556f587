import API from "@/lib/axios";
import { AxiosError } from "axios";

// Types for test question management
export interface TestQuestionItem {
  id: string;
  question: string;
  option_a: string;
  option_b: string;
  option_c: string;
  option_d: string;
  option_true: 'A' | 'B' | 'C' | 'D';
  status: 'active' | 'inactive';
  created_at?: string;
  updated_at?: string;
}

export interface TestQuestionListResponse {
  data: TestQuestionItem[];
  totalData: number;
  totalPage: number;
}

export interface AddQuestionToTestRequest {
  token: string;
  id_test: string;
  type: 'existing' | 'new';
  // For existing questions
  id_questions?: string[]; // Array of question IDs
  // For new questions
  questions?: Array<{
    question: string;
    option_a: string;
    option_b: string;
    option_c: string;
    option_d: string;
    option_true: 'A' | 'B' | 'C' | 'D';
  }>;
}

export interface DeleteQuestionFromTestRequest {
  token: string;
  id_test: string;
  id_questions: string[]; // Array of question IDs to remove
}

// List questions in a specific test
export const listQuestionsInTestAPI = async (
  token: string,
  id_test: string,
  search?: string,
  page: number = 1,
  limit: number = 10
): Promise<TestQuestionListResponse> => {
  try {
    const requestData = {
      token,
      id_test,
      search: search || "",
      page,
      limit,
    };

    const response = await API.post<{
      code: number;
      mess: string;
      data: TestQuestionItem[];
      totalData?: {
        totalData: number;
        totalPage: number;
      };
    }>("/listQuestionsInTestAPI", requestData);

    if (response.data.code === 0 && response.data.data) {
      return {
        data: response.data.data,
        totalData: response.data.totalData?.totalData || 0,
        totalPage: response.data.totalData?.totalPage || 1,
      };
    } else {
      throw new Error(response.data.mess || "Không thể tải danh sách câu hỏi trong bài kiểm tra.");
    }
  } catch (err) {
    const error = err as Error;
    throw new Error(error.message || "Lỗi không xác định khi tải danh sách câu hỏi.");
  }
};

// Add questions to test (existing or new)
export const addQuestionToTestAPI = async (
  data: AddQuestionToTestRequest
): Promise<{ code: number; mess: string; data?: any }> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
      data?: any;
    }>("/addQuestionToTestAPI", data);

    if (response.data.code === 0) {
      return response.data;
    } else {
      throw new Error(response.data.mess || "Không thể thêm câu hỏi vào bài kiểm tra.");
    }
  } catch (err) {
    const error = err as Error;
    throw new Error(error.message || "Lỗi không xác định khi thêm câu hỏi.");
  }
};

// Remove questions from test
export const deleteQuestionFromTestAPI = async (
  data: DeleteQuestionFromTestRequest
): Promise<{ code: number; mess: string }> => {
  try {
    const response = await API.post<{
      code: number;
      mess: string;
    }>("/deleteQuestionFromTestAPI", {
      id_question: data.id_questions,
      id_test: data.id_test,
      token: data.token,
    });

    if (response.data.code === 0) {
      return response.data;
    } else {
      throw new Error(response.data.mess || "Không thể xóa câu hỏi khỏi bài kiểm tra.");
    }
  } catch (err) {
    const error = err as Error;
    throw new Error(error.message || "Lỗi không xác định khi xóa câu hỏi.");
  }
};

// Helper function to validate question data
export const validateQuestionData = (question: {
  question: string;
  option_a: string;
  option_b: string;
  option_c: string;
  option_d: string;
  option_true: string;
}): string[] => {
  const errors: string[] = [];

  if (!question.question.trim()) {
    errors.push("Câu hỏi không được để trống");
  }
  if (!question.option_a.trim()) {
    errors.push("Đáp án A không được để trống");
  }
  if (!question.option_b.trim()) {
    errors.push("Đáp án B không được để trống");
  }
  if (!question.option_c.trim()) {
    errors.push("Đáp án C không được để trống");
  }
  if (!question.option_d.trim()) {
    errors.push("Đáp án D không được để trống");
  }
  if (!['A', 'B', 'C', 'D'].includes(question.option_true)) {
    errors.push("Đáp án đúng phải là A, B, C hoặc D");
  }

  return errors;
};

// Helper function to format question for display
export const formatQuestionForDisplay = (question: TestQuestionItem): string => {
  const maxLength = 100;
  if (question.question.length <= maxLength) {
    return question.question;
  }
  return question.question.substring(0, maxLength) + "...";
};

// Helper function to get correct answer text
export const getCorrectAnswerText = (question: TestQuestionItem): string => {
  switch (question.option_true) {
    case 'A':
      return question.option_a;
    case 'B':
      return question.option_b;
    case 'C':
      return question.option_c;
    case 'D':
      return question.option_d;
    default:
      return 'N/A';
  }
};
