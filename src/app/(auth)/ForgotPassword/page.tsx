'use client'
import React, { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Phone, Shield, Lock, Check } from 'lucide-react';
import { toast } from 'sonner';

const ForgotPassword = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [otp, setOtp] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);

  const handlePhoneSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!phoneNumber) {
      toast.error('<PERSON>ui lòng nhập số điện thoại');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('https://devqlth.phoenixtech.vn/apis/requestCodeForgotPasswordMemberAPI', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phone: phoneNumber
        })
      });

      const data = await response.json();
      console.log('Phone API response:', data);

      if (data.code === 1) {
        toast.success('Mã OTP đã được gửi đến email của bạn');
        setCurrentStep(2);
      } else {
        toast.error(data.mess || 'Số điện thoại không tồn tại');
      }
    } catch (error) {
      console.error('Phone API error:', error);
      toast.error('Có lỗi xảy ra, vui lòng thử lại');
    } finally {
      setLoading(false);
    }
  };

  const handleOtpSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!otp) {
      toast.error('Vui lòng nhập mã OTP');
      return;
    }
    if (otp.length !== 4) {
      toast.error('Mã OTP phải có 4 chữ số');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('https://devqlth.phoenixtech.vn/apis/checkCodeOtMemberAPI', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phone: phoneNumber,
          code: otp
        })
      });

      const data = await response.json();
      console.log('OTP API response:', data);

      if (data.code === 1) {
        toast.success('Xác thực OTP thành công');
        setCurrentStep(3);
      } else {
        toast.error(data.mess || 'Mã OTP không đúng');
      }
    } catch (error) {
      console.error('OTP API error:', error);
      toast.error('Có lỗi xảy ra, vui lòng thử lại');
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newPassword || !confirmPassword) {
      toast.error('Vui lòng điền đầy đủ thông tin');
      return;
    }
    if (newPassword !== confirmPassword) {
      toast.error('Mật khẩu xác nhận không khớp');
      return;
    }
    if (newPassword.length < 6) {
      toast.error('Mật khẩu phải có ít nhất 6 ký tự');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('https://devqlth.phoenixtech.vn/apis/saveNewPassMemberAPI', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phone: phoneNumber,
          code: otp,
          passNew: newPassword,
          passAgain: confirmPassword
        })
      });

      const data = await response.json();
      console.log('Password API response:', data);

      if (data.code === 1) {
        toast.success('Đặt lại mật khẩu thành công');
        // Redirect to login page after success
        setTimeout(() => {
          window.location.href = '/login';
        }, 2000);
      } else {
        toast.error(data.mess || 'Có lỗi xảy ra khi đặt lại mật khẩu');
      }
    } catch (error) {
      console.error('Password API error:', error);
      toast.error('Có lỗi xảy ra, vui lòng thử lại');
    } finally {
      setLoading(false);
    }
  };

  const steps = [
    { number: 1, title: 'Số điện thoại', icon: Phone },
    { number: 2, title: 'Xác thực OTP', icon: Shield },
    { number: 3, title: 'Mật khẩu mới', icon: Lock }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <Link href="/login" className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Quay lại đăng nhập
          </Link>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Quên mật khẩu</h1>
          <p className="text-gray-600">Đặt lại mật khẩu của bạn</p>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex justify-between items-center">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = currentStep === step.number;
              const isCompleted = currentStep > step.number;
              
              return (
                <div key={step.number} className="flex flex-col items-center flex-1">
                  <div className={`
                    flex items-center justify-center w-10 h-10 rounded-full border-2 mb-2
                    ${isCompleted ? 'bg-green-500 border-green-500 text-white' : 
                      isActive ? 'bg-blue-600 border-blue-600 text-white' : 
                      'bg-white border-gray-300 text-gray-400'}
                  `}>
                    {isCompleted ? (
                      <Check className="h-5 w-5" />
                    ) : (
                      <Icon className="h-5 w-5" />
                    )}
                  </div>
                  <span className={`text-xs font-medium ${
                    isActive || isCompleted ? 'text-blue-600' : 'text-gray-400'
                  }`}>
                    {step.title}
                  </span>
                  {index < steps.length - 1 && (
                    <div className={`
                      absolute h-0.5 w-16 mt-5 ml-16
                      ${isCompleted ? 'bg-green-500' : 'bg-gray-300'}
                    `} />
                  )}
                </div>
              );
            })}
          </div>
        </div>

        <Card className="shadow-xl border-0">
          <CardHeader className="space-y-1">
            <CardTitle className="text-xl text-center">
              {currentStep === 1 && 'Nhập số điện thoại'}
              {currentStep === 2 && 'Xác thực OTP'}
              {currentStep === 3 && 'Đặt mật khẩu mới'}
            </CardTitle>
            <CardDescription className="text-center">
              {currentStep === 1 && 'Nhập số điện thoại đã đăng ký tài khoản'}
              {currentStep === 2 && 'Nhập mã OTP được gửi đến email của bạn'}
              {currentStep === 3 && 'Tạo mật khẩu mới cho tài khoản của bạn'}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Step 1: Phone Number */}
            {currentStep === 1 && (
              <form onSubmit={handlePhoneSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="phone">Số điện thoại</Label>
                  <Input
                    id="phone"
                    type="tel"
                    placeholder="Nhập số điện thoại (VD: 0123456789)"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    className="h-11"
                    disabled={loading}
                  />
                </div>
                <Button 
                  type="submit" 
                  className="w-full h-11 bg-blue-600 hover:bg-blue-700"
                  disabled={loading}
                >
                  {loading ? 'Đang gửi...' : 'Gửi mã OTP'}
                </Button>
              </form>
            )}

            {/* Step 2: OTP Verification */}
            {currentStep === 2 && (
              <form onSubmit={handleOtpSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="otp">Mã OTP</Label>
                  <Input
                    id="otp"
                    type="text"
                    placeholder="Nhập mã OTP 4 chữ số"
                    value={otp}
                    onChange={(e) => setOtp(e.target.value)}
                    maxLength={4}
                    className="h-11 text-center text-lg tracking-widest"
                    disabled={loading}
                  />
                  <p className="text-sm text-gray-500">
                    Mã OTP đã được gửi đến email liên kết với số {phoneNumber}
                  </p>
                </div>
                <div className="flex gap-3">
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => setCurrentStep(1)}
                    className="flex-1 h-11"
                    disabled={loading}
                  >
                    Quay lại
                  </Button>
                  <Button 
                    type="submit" 
                    className="flex-1 h-11 bg-blue-600 hover:bg-blue-700"
                    disabled={loading}
                  >
                    {loading ? 'Đang xác thực...' : 'Xác thực'}
                  </Button>
                </div>
              </form>
            )}

            {/* Step 3: New Password */}
            {currentStep === 3 && (
              <form onSubmit={handlePasswordSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="newPassword">Mật khẩu mới</Label>
                  <Input
                    id="newPassword"
                    type="password"
                    placeholder="Nhập mật khẩu mới"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    className="h-11"
                    disabled={loading}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Xác nhận mật khẩu</Label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    placeholder="Nhập lại mật khẩu mới"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className="h-11"
                    disabled={loading}
                  />
                </div>
                <div className="flex gap-3">
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => setCurrentStep(2)}
                    className="flex-1 h-11"
                    disabled={loading}
                  >
                    Quay lại
                  </Button>
                  <Button 
                    type="submit" 
                    className="flex-1 h-11 bg-blue-600 hover:bg-blue-700"
                    disabled={loading}
                  >
                    {loading ? 'Đang lưu...' : 'Đặt lại mật khẩu'}
                  </Button>
                </div>
              </form>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ForgotPassword;