"use client";
import React, { useEffect, useState, useCallback } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/redux/store";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Calendar,
  Users,
  BookOpen,
  FileText,
  CalendarDays,
  ChevronRight,
  Eye,
  MapPin,
  Loader2,
  AlertTriangle,
} from "lucide-react";
import Link from "next/link";
import { getAuthToken } from "@/utils/getAuthToken";
import { listClasseAPI } from "@/api/managerClassAPI";
import { listScheduleAPI } from "@/api/teacher/managerScheduleAPI";
import { ClassRoom } from "@/types/managerclass";
import { toast } from "sonner";

// Types for Teacher Dashboard
interface DashboardStats {
  totalClasses: number;
  totalStudents: number;
  todayClasses: number;
  thisWeekClasses: number;
}

interface ScheduleSession {
  id: number;
  id_course: number;
  date_of_week: number;
  week_start: string;
  week_end: string;
  period_start: string;
  period_end: string;
  room: string;
  day_name: string;
}

interface Course {
  id: number;
  name: string;
  code: string;
  groups: string;
  id_teacher: number;
  id_semester: number;
  id_subject: number;
  fee: number;
  number_students: number;
  slug: string;
  teacher_info: {
    id: number;
    full_name: string;
    email: string;
    phone: string;
  };
  semester_info: {
    id: number;
    name: string;
    code: string;
    start_date: string;
    end_date: string;
  };
  subject_info: {
    id: number;
    name: string;
    keyword: string;
  };
  schedule_sessions: ScheduleSession[];
}

interface UpcomingEvent {
  id: string;
  title: string;
  date: string;
  time: string;
  type: 'class';
  location?: string;
  course: Course;
}

// Utility functions
const getWeekRange = (date: Date) => {
  const startOfWeek = new Date(date);
  const day = startOfWeek.getDay();
  const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1);
  startOfWeek.setDate(diff);

  const endOfWeek = new Date(startOfWeek);
  endOfWeek.setDate(startOfWeek.getDate() + 6);

  return {
    start: startOfWeek.toISOString().split('T')[0],
    end: endOfWeek.toISOString().split('T')[0]
  };
};

const isToday = (dateString: string) => {
  const today = new Date();
  const date = new Date(dateString);
  return today.toDateString() === date.toDateString();
};

const isThisWeek = (dateString: string) => {
  const today = new Date();
  const date = new Date(dateString);
  const weekRange = getWeekRange(today);
  const dateStr = date.toISOString().split('T')[0];
  return dateStr >= weekRange.start && dateStr <= weekRange.end;
};

const CountUp = ({ end }: { end: number }) => {
  const [count, setCount] = useState(0);

  useEffect(() => {
    let start = 0;
    const duration = 1000;
    const increment = end / (duration / 10);

    const timer = setInterval(() => {
      start += increment;
      if (start < end) {
        setCount(Math.ceil(start));
      } else {
        setCount(end);
        clearInterval(timer);
      }
    }, 10);

    return () => clearInterval(timer);
  }, [end]);

  return <>{count}</>;
};

export default function TeacherDashboard() {
  const userInfo = useSelector((state: RootState) => state.user.currentUser);
  const accessToken = getAuthToken();

  // State management
  const [stats, setStats] = useState<DashboardStats>({
    totalClasses: 0,
    totalStudents: 0,
    todayClasses: 0,
    thisWeekClasses: 0,
  });
  const [classes, setClasses] = useState<ClassRoom[]>([]);
  const [upcomingEvents, setUpcomingEvents] = useState<UpcomingEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // API functions
  const fetchClasses = useCallback(async () => {
    if (!accessToken || !userInfo?.id) return;

    try {
      const response = await listClasseAPI({}, 1, accessToken, userInfo.id, 100);
      setClasses(response.data);

      // Calculate stats from classes
      const totalStudents = response.data.reduce((sum, classRoom) =>
        sum + (classRoom.students?.length || 0), 0
      );

      setStats(prev => ({
        ...prev,
        totalClasses: response.data.length,
        totalStudents,
      }));
    } catch (error) {
      console.error('Error fetching classes:', error);
      toast.error('Không thể tải danh sách lớp học');
    }
  }, [accessToken, userInfo?.id]);

  const fetchSchedule = useCallback(async () => {
    if (!accessToken) return;

    try {
      const today = new Date();
      const weekRange = getWeekRange(today);

      const response = await listScheduleAPI(accessToken, weekRange.start, weekRange.end);
      const courses = response.data || [];

      // Process schedule data to create upcoming events
      const events: UpcomingEvent[] = [];
      let todayClassesCount = 0;
      let thisWeekClassesCount = 0;

      courses.forEach((course: Course) => {
        course.schedule_sessions.forEach((session: ScheduleSession) => {
          // Calculate the actual date for this session
          const sessionDate = new Date(session.week_start);
          const dayOffset = session.date_of_week === 1 ? 6 : session.date_of_week - 2; // Convert to 0-6 range
          sessionDate.setDate(sessionDate.getDate() + dayOffset);

          const sessionDateStr = sessionDate.toISOString().split('T')[0];

          if (isToday(sessionDateStr)) {
            todayClassesCount++;
          }

          if (isThisWeek(sessionDateStr)) {
            thisWeekClassesCount++;
          }

          // Add to upcoming events (next 7 days)
          const nextWeek = new Date();
          nextWeek.setDate(nextWeek.getDate() + 7);

          if (sessionDate >= today && sessionDate <= nextWeek) {
            events.push({
              id: `${course.id}-${session.id}`,
              title: course.name,
              date: sessionDateStr,
              time: session.period_start,
              type: 'class',
              location: session.room,
              course,
            });
          }
        });
      });

      // Sort events by date and time
      events.sort((a, b) => {
        const dateA = new Date(`${a.date} ${a.time}`);
        const dateB = new Date(`${b.date} ${b.time}`);
        return dateA.getTime() - dateB.getTime();
      });

      setUpcomingEvents(events.slice(0, 5)); // Show only next 5 events
      setStats(prev => ({
        ...prev,
        todayClasses: todayClassesCount,
        thisWeekClasses: thisWeekClassesCount,
      }));

    } catch (error) {
      console.error('Error fetching schedule:', error);
      toast.error('Không thể tải lịch giảng dạy');
    }
  }, [accessToken]);

  // Load data on component mount
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      setError(null);

      try {
        await Promise.all([fetchClasses(), fetchSchedule()]);
      } catch (error) {
        setError('Có lỗi xảy ra khi tải dữ liệu');
        console.error('Error loading dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    if (accessToken && userInfo?.id) {
      loadData();
    }
  }, [fetchClasses, fetchSchedule, accessToken, userInfo?.id]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const formatTime = (timeString: string) => {
    return timeString;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Đang tải dữ liệu...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>
            Thử lại
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto p-6 space-y-8">
        {/* Welcome Header */}
        <header className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Chào mừng trở lại, {userInfo?.name || "Giáo viên"}!
          </h1>
          <p className="text-gray-600 mt-2">
            Tổng quan hoạt động giảng dạy và quản lý lớp học của bạn
          </p>
        </header>

        {/* Overview Statistics */}
        <section className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-blue-100">
                Tổng số lớp
              </CardTitle>
              <BookOpen className="h-5 w-5 text-blue-200" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                <CountUp end={stats.totalClasses} />
              </div>
              <p className="text-xs text-blue-100 mt-1">
                Đang phụ trách
              </p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-green-100">
                Tổng học sinh
              </CardTitle>
              <Users className="h-5 w-5 text-green-200" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                <CountUp end={stats.totalStudents} />
              </div>
              <p className="text-xs text-green-100 mt-1">
                Tất cả các lớp
              </p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-red-500 to-red-600 text-white">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-red-100">
                Lớp hôm nay
              </CardTitle>
              <Calendar className="h-5 w-5 text-red-200" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                <CountUp end={stats.todayClasses} />
              </div>
              <p className="text-xs text-red-100 mt-1">
                Buổi học
              </p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-indigo-500 to-indigo-600 text-white">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-indigo-100">
                Tuần này
              </CardTitle>
              <CalendarDays className="h-5 w-5 text-indigo-200" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                <CountUp end={stats.thisWeekClasses} />
              </div>
              <p className="text-xs text-indigo-100 mt-1">
                Tổng buổi học
              </p>
            </CardContent>
          </Card>
        </section>


        {/* Quick Actions Section */}
        <section>
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">
            Thao tác nhanh
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Link href="/teacherClass">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="p-3 rounded-lg bg-blue-500 text-white group-hover:scale-110 transition-transform">
                      <BookOpen className="h-6 w-6" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                        Quản lý lớp học
                      </h3>
                      <p className="text-sm text-gray-600 mt-1">
                        Xem và quản lý các lớp học
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>

            <Link href="/teacherSchedule">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="p-3 rounded-lg bg-purple-500 text-white group-hover:scale-110 transition-transform">
                      <CalendarDays className="h-6 w-6" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                        Xem lịch giảng
                      </h3>
                      <p className="text-sm text-gray-600 mt-1">
                        Kiểm tra lịch giảng dạy
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>

            <Link href="/teacherGrade">
              <Card className="hover:shadow-lg transition-shadow cursor-pointer group">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="p-3 rounded-lg bg-green-500 text-white group-hover:scale-110 transition-transform">
                      <FileText className="h-6 w-6" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                        Quản lý điểm số
                      </h3>
                      <p className="text-sm text-gray-600 mt-1">
                        Xem và chấm điểm học sinh
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          </div>
        </section>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Class Management */}
          <section>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-semibold text-gray-900">
                Lớp học của tôi
              </h2>
              <Link href="/teacherClass">
                <Button variant="outline" size="sm">
                  <ChevronRight className="h-4 w-4 mr-2" />
                  Xem tất cả
                </Button>
              </Link>
            </div>
            {classes.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">Chưa có lớp học nào được phân công</p>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {classes.slice(0, 4).map((classRoom) => (
                  <Card key={classRoom.id} className="hover:shadow-md transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg font-semibold text-gray-900">
                          {classRoom.name}
                        </CardTitle>
                        <Badge variant="secondary">
                          {classRoom.subject}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center text-sm text-gray-600">
                          <Users className="h-4 w-4 mr-2" />
                          {classRoom.students?.length || 0} học sinh
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <BookOpen className="h-4 w-4 mr-2" />
                          {classRoom.major?.name || 'Chưa có thông tin'}
                        </div>
                        <div className="flex space-x-2 mt-4">
                          <Link href={`/teacherClass/${classRoom.id}`} className="flex-1">
                            <Button size="sm" variant="outline" className="w-full">
                              <Eye className="h-4 w-4 mr-2" />
                              Xem chi tiết
                            </Button>
                          </Link>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </section>

          {/* Calendar/Schedule View */}
          <section>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-semibold text-gray-900">
                Lịch sắp tới
              </h2>
              <Link href="/teacherSchedule">
                <Button variant="outline" size="sm">
                  <Calendar className="h-4 w-4 mr-2" />
                  Xem lịch đầy đủ
                </Button>
              </Link>
            </div>
            <Card>
              <CardContent className="p-6">
                {upcomingEvents.length === 0 ? (
                  <div className="text-center py-8">
                    <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">Không có lịch học sắp tới</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {upcomingEvents.map((event) => (
                      <div
                        key={event.id}
                        className="flex items-center space-x-4 p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                      >
                        <div className="p-2 rounded-full bg-blue-100 text-blue-600">
                          <BookOpen className="h-4 w-4" />
                        </div>
                        <div className="flex-1">
                          <p className="font-medium text-gray-900">
                            {event.title}
                          </p>
                          <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                            <span>{formatDate(event.date)} - {formatTime(event.time)}</span>
                            {event.location && (
                              <span className="flex items-center">
                                <MapPin className="h-3 w-3 mr-1" />
                                {event.location}
                              </span>
                            )}
                          </div>
                          <p className="text-xs text-gray-500 mt-1">
                            {event.course.subject_info.name} - {event.course.groups}
                          </p>
                        </div>
                        <Badge variant="default">
                          Lớp học
                        </Badge>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </section>
        </div>
      </div>
    </div>
  );
}
