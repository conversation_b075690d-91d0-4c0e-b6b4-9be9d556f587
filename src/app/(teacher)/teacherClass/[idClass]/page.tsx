"use client";
import { listStudentsInClassAPI } from "@/api/managerClassAPI";
import { detailStudentAPI } from "@/api/managerStudentAPI";
import { FiltersStudent } from "@/components/TeacherClass/StudentInClass/StudentClassFilter";
import StudentClassTable from "@/components/TeacherClass/StudentInClass/StudentClassTable";
import { StudentDetailDialog } from "@/components/TeacherClass/StudentInClass/StudentDetailDialog";
import { ClassFilter, StudentFilter } from "@/types/managerclass";
import { Student } from "@/types/managerStudent";
import { getAuthToken } from "@/utils/getAuthToken";
import { use, useCallback, useEffect, useState } from "react";

interface DetailPageProps {
  params: Promise<{
    idClass: string;
  }>; // Cập nhật kiểu của params
}

const ITEMS_PER_PAGE = 20;

export default function DetailClass({ params }: DetailPageProps) {
  const { idClass } = use(params);
  const accessToken = getAuthToken();
  const [filters, setFilters] = useState<StudentFilter>({});

  const [dataClass, setDataClass] = useState<any[]>([]);
  const [infoClass, setInfoClass] = useState<any>({});

  const [studentDetail, setStudentDetail] = useState<Student>({} as Student);
  const [isOpenDetail, setIsOpenDetail] = useState(false);

  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  const fetchListClass = useCallback(
    async (page = 1, currentFilters = filters) => {
      if (!accessToken || !idClass) return;
      setIsLoading(true);
      try {
        const response = await listStudentsInClassAPI(
          currentFilters,
          page,
          accessToken,
          idClass
        );
        setDataClass(response.data);
        setInfoClass(response.infoClass);
        setTotalRecords(response.totalData);
        setTotalPages(Math.ceil(response.totalData / ITEMS_PER_PAGE));
      } catch (error) {
        console.error("Failed to fetch data:", error);
        setDataClass([]);
        setInfoClass(null);
        setTotalRecords(0);
        setTotalPages(1);
      } finally {
        setIsLoading(false);
      }
    },
    [accessToken, filters, idClass] // Include filters here
  );

  useEffect(() => {
    fetchListClass(currentPage, filters);
  }, [fetchListClass, currentPage, filters]);

  const handleFilterChange = (
    filterName: keyof StudentFilter,
    value: string | undefined
  ) => {
    setFilters((prev) => ({ ...prev, [filterName]: value }));
  };

  const handleSearch = () => {
    setCurrentPage(1);
    // fetchRegistrations will be called by useEffect due to filters/currentPage change
  };

  const handleResetFilters = () => {
    setFilters({});
    setCurrentPage(1);
    // fetchRegistrations will be called by useEffect
  };

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages && newPage !== currentPage) {
      setCurrentPage(newPage);
    }
  };

  const closeDetailDialog = () => {
    setIsOpenDetail(false);
    setStudentDetail({} as Student);
  };

  const handleCloseDetail = () => {
    closeDetailDialog();
  };

  const openDetailDialog = async (studentId: any) => {
    if (!accessToken) return;
    try {
      const response = await detailStudentAPI(studentId, accessToken);
      setStudentDetail(response as Student);
      setIsOpenDetail(true);
    } catch (error) {
      console.error("Failed to fetch data:", error);
      setIsOpenDetail(false);
    }
  };

  return (
    <div className="container mx-auto p-4 md:p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-xl md:text-2xl font-bold">
          Quản lý Học viên lớp: {infoClass?.name}
        </h1>
      </div>

      <FiltersStudent
        filters={filters}
        onFilterChange={handleFilterChange}
        onSearch={handleSearch}
        onResetFilters={handleResetFilters}
      />

      <StudentClassTable
        students={dataClass}
        totalRecords={totalRecords}
        currentPage={currentPage}
        itemsPerPage={ITEMS_PER_PAGE}
        isLoading={isLoading}
        onOpenDetail={openDetailDialog}
      />

      <StudentDetailDialog
        student={studentDetail}
        isOpen={isOpenDetail}
        onOpenChange={setIsOpenDetail}
      />
    </div>
  );
}
