"use client";
import { listAcademicYearsAPI, listMajorAPI } from "@/api/categoryAPI";
import { listClasseAPI } from "@/api/managerClassAPI";
import { FiltersClass } from "@/components/TeacherClass/TeacherClassFilter";
import TeacherClassTable from "@/components/TeacherClass/TeacherClassTable";
import { PageHeader } from "@/components/TeacherClass/TeacherPageHeader";
import { RootState } from "@/redux/store";
import { AcademicYear, Category } from "@/types/category";
import { ClassFilter, ClassRoom } from "@/types/managerclass";
import { getAuthToken } from "@/utils/getAuthToken";
import { useCallback, useEffect, useState } from "react";
import { useSelector } from "react-redux";

const ITEMS_PER_PAGE = 20;

export default function TeacherClass() {
  const accessToken = getAuthToken();
  const user_info = useSelector((state: RootState) => state.user.currentUser);
  const [dataClass, setDataClass] = useState<ClassRoom[]>([]);
  const [filters, setFilters] = useState<ClassFilter>({});

  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  const [listAcademicYear, setListAcademicYear] = useState<AcademicYear[]>([]);
  const [listMajor, setListMajor] = useState<Category[]>([]);

  const fetchListClass = useCallback(
    async (page = 1, currentFilters = filters) => {
      if (!accessToken || !user_info?.id) return;
      setIsLoading(true);
      try {
        const response = await listClasseAPI(
          currentFilters,
          page,
          accessToken,
          user_info.id
        );
        setDataClass(response.data);
        setTotalRecords(response.totalData);
        setTotalPages(Math.ceil(response.totalData / ITEMS_PER_PAGE));
      } catch (error) {
        console.error("Failed to fetch data:", error);
        setDataClass([]);
        setTotalRecords(0);
        setTotalPages(1);
      } finally {
        setIsLoading(false);
      }
    },
    [accessToken, filters, user_info?.id] // Include filters here
  );

  useEffect(() => {
    fetchListClass(currentPage, filters);
  }, [fetchListClass, currentPage, filters]);

  const fetchAceademicYear = useCallback(async () => {
    if (!accessToken) return;
    setIsLoading(true);
    try {
      const response = await listAcademicYearsAPI();
      setListAcademicYear(response);
    } catch (error) {
      console.error("Failed to fetch data:", error);
      setListAcademicYear([]);
    } finally {
      setIsLoading(false);
    }
  }, [accessToken]);

  const fetchMajor = useCallback(async () => {
    if (!accessToken) return;
    setIsLoading(true);
    try {
      const response = await listMajorAPI({}, 1, accessToken);
      setListMajor(response.data);
    } catch (error) {
      console.error("Failed to fetch data:", error);
      setListMajor([]);
    } finally {
      setIsLoading(false);
    }
  }, [accessToken]);

  useEffect(() => {
    fetchAceademicYear();
    fetchMajor();
  }, [fetchAceademicYear, fetchMajor]);

  const handleFilterChange = (
    filterName: keyof ClassFilter,
    value: string | undefined
  ) => {
    setFilters((prev) => ({ ...prev, [filterName]: value }));
  };

  const handleSearch = () => {
    setCurrentPage(1);
    // fetchRegistrations will be called by useEffect due to filters/currentPage change
  };

  const handleResetFilters = () => {
    setFilters({});
    setCurrentPage(1);
    // fetchRegistrations will be called by useEffect
  };

  return (
    <div className="container mx-auto p-4 md:p-6 space-y-6">
      <PageHeader name="Lớp học" isAdd={false} />
      <FiltersClass
        filters={filters}
        onFilterChange={handleFilterChange}
        onSearch={handleSearch}
        onResetFilters={handleResetFilters}
        listAcademicYear={listAcademicYear}
        listMajor={listMajor}
      />
      <TeacherClassTable
        classes={dataClass}
        totalRecords={totalRecords}
        currentPage={currentPage}
        itemsPerPage={ITEMS_PER_PAGE}
        isLoading={isLoading}
      />
    </div>
  );
}
