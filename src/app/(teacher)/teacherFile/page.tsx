"use client";
import { listSubjectAP<PERSON> } from "@/api/categoryAPI";
import {
  addDocumentSubjectFileAPI,
  deleteDocumentFileAPI,
  detailDocumentSubjectFileAPI,
  listDocumentSubjectFileAPI,
  updateDocumentSubjectFileAPI,
} from "@/api/teacher/managerFileAPi";
import { PaginationComponent } from "@/components/Pagination";
import { FileFormDialog } from "@/components/TeacherFile/TeacherFileDialog";
import { FiltersCard } from "@/components/TeacherFile/TeacherFileFilter";
import FileTable from "@/components/TeacherFile/TeacherFileTable";
import { Category } from "@/types/category";
import {
  DocumentFilter,
  DocumentFormData,
  DocumentItem,
  SubjectItem,
} from "@/types/managerFile";
import { getAuthToken } from "@/utils/getAuthToken";
import { useCallback, useEffect, useState } from "react";
import * as z from "zod";
import { toast } from "sonner";
import { <PERSON>sol<PERSON>, Submit<PERSON>and<PERSON>, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { DeleteConfirmation } from "@/components/DeleteConfirmation";
import { Button } from "@/components/ui/button";

const ITEMS_PER_PAGE = 20;

const fileFormSchema = z.object({
  name: z.string().min(1, "Vui lòng nhập tên."),
  id_subject: z.string().min(1, "Vui lòng chọn môn học."),
  note: z.string().optional(),
  link_file: z
    .custom<File | string | null>() // Cho phép File, string (URL), hoặc null
    .refine(
      (file) => {
        if (file === null) return true; // null là hợp lệ
        if (typeof file === "string") return true; // string (URL) là hợp lệ
        if (file instanceof File) return true; // File là hợp lệ
        return false;
      },
      {
        message: "Định dạng file hoặc URL không hợp lệ",
      }
    )
    .optional(),
});

export default function TeacherFilePage() {
  const accessToken = getAuthToken();

  const [files, setFiles] = useState<DocumentItem[]>([]);
  const [subjectOptions, setSubjectOptions] = useState<Category[]>([]);
  const [filters, setFilters] = useState<DocumentFilter>({});

  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingFileId, setEditingFileId] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [fileToDeleteId, setFileToDeleteId] = useState<string | null>(null);

  const {
    control: fileControl,
    handleSubmit: handleFileSubmit,
    reset: FileReset,
    formState: { errors: fileErrors, isSubmitting: isFileFormSubmitting },
  } = useForm<DocumentFormData>({
    resolver: zodResolver(
      fileFormSchema
    ) as unknown as Resolver<DocumentFormData>,
    defaultValues: {
      name: "",
      id_subject: "",
      note: "",
      link_file: null,
    },
  });

  const fetchInfoFile = useCallback(
    async (page = 1, currentFilters = filters) => {
      if (!accessToken) return;
      setIsLoading(true);
      try {
        const response = await listDocumentSubjectFileAPI(
          currentFilters,
          page,
          accessToken
        );
        setFiles(response.data);
        setTotalRecords(response.totalData);
        setTotalPages(Math.ceil(response.totalData / ITEMS_PER_PAGE));
      } catch (error) {
        console.error("Failed to fetch registrations:", error);
        setFiles([]);
        setTotalRecords(0);
        setTotalPages(1);
      } finally {
        setIsLoading(false);
      }
    },
    [accessToken, filters] // Include filters here
  );

  useEffect(() => {
    fetchInfoFile(currentPage, filters);
  }, [fetchInfoFile, currentPage, filters]); // Add filters to dependency array

  const fetchSubjectOptions = useCallback(async () => {
    if (!accessToken) return;
    try {
      const response = await listSubjectAPI({}, 1, accessToken);
      setSubjectOptions(response.data);
    } catch (error) {
      console.error("Failed to fetch subject options:", error);
      setSubjectOptions([]);
    }
  }, [accessToken]);

  useEffect(() => {
    fetchSubjectOptions();
  }, [fetchSubjectOptions]);

  const handleFilterChange = (
    filterName: keyof DocumentFilter,
    value: string | undefined
  ) => {
    setFilters((prev) => ({ ...prev, [filterName]: value }));
  };

  const handleSearch = () => {
    setCurrentPage(1);
    // fetchRegistrations will be called by useEffect due to filters/currentPage change
  };

  const handleResetFilters = () => {
    setFilters({});
    setCurrentPage(1);
    // fetchRegistrations will be called by useEffect
  };

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages && newPage !== currentPage) {
      setCurrentPage(newPage);
    }
  };

  const openAddForm = () => {
    FileReset({
      name: "",
      id_subject: "",
      note: "",
      link_file: null,
    });
    setEditingFileId(null);
    setIsFormOpen(true);
  };

  const openEditForm = async (id: string) => {
    if (!accessToken) return;
    try {
      const regData = await detailDocumentSubjectFileAPI(id, accessToken);
      if (regData) {
        FileReset({
          name: regData.name,
          id_subject: regData.id_subject,
          note: regData.note || "",
          link_file: regData.link_file,
        });
        setEditingFileId(id);
        setIsFormOpen(true);
      }
    } catch (error) {
      console.error("Failed to fetch registration details:", error);
    }
  };

  const onFormSubmit: SubmitHandler<DocumentFormData> = async (data) => {
    if (!accessToken) return;
    try {
      const formData = new FormData();
      formData.append("token", accessToken);
      formData.append("name", data.name);
      formData.append("id_subject", data.id_subject);
      formData.append("note", data.note || "");
      formData.append("link_file", data.link_file as File);

      if (editingFileId) {
        formData.append("id", editingFileId);
        await updateDocumentSubjectFileAPI(formData);
        console.log(
          "Call update API for ID:",
          editingFileId,
          "with data:",
          formData
        ); // Replace with actual update API call
        toast.success("Tài liệu đã được cập nhật thành công!"); // Replace with toast
      } else {
        // Use the new add API function
        await addDocumentSubjectFileAPI(formData);
        toast.success("Tài liệu đã được thêm thành công!"); // Replace with toast
      }
      setIsFormOpen(false);
      fetchInfoFile(editingFileId ? currentPage : 1); // Refresh list
    } catch (error) {
      console.error("Failed to save registration:", error);
      // Display the specific error message from the API
      toast.error(
        `Lỗi khi lưu tài liệu: ${
          error instanceof Error ? error.message : "Lỗi không xác định"
        }`
      ); // Replace with toast
    }
  };

  const handleDelete = (id: string) => {
    setFileToDeleteId(id);
    setIsDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!accessToken || !fileToDeleteId) return;

    setIsLoading(true); // Optional: show loading state during deletion
    try {
      const res = await deleteDocumentFileAPI(fileToDeleteId, accessToken);
      if (res.code === 1) {
        toast.success("Đã xóa tài liệu thành công!");
        fetchInfoFile(currentPage); // Refresh list
      } else {
        toast.error("Đã có lỗi xảy ra khi xóa tài liệu");
      }
    } catch (error) {
      console.error("Failed to delete registration:", error);
      toast.error(
        `Lỗi khi xóa tài liệu: ${
          error instanceof Error ? error.message : "Lỗi không xác định"
        }`
      ); // Use toast
    } finally {
      setIsLoading(false); // Optional: hide loading state
      setIsDeleteDialogOpen(false); // Close dialog
      setFileToDeleteId(null); // Clear ID
    }
  };

  return (
    <div className="container mx-auto p-4 md:p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Quản lý tài liệu</h1>
        <Button onClick={openAddForm} color="green" variant="default">
          Thêm tài liệu
        </Button>
      </div>
      <FiltersCard
        filters={filters}
        subjectOptions={subjectOptions}
        onFilterChange={handleFilterChange}
        onSearch={handleSearch}
        onResetFilters={handleResetFilters}
      />

      <FileTable
        files={files}
        isLoading={isLoading}
        totalRecords={totalRecords}
        currentPage={currentPage}
        itemsPerPage={ITEMS_PER_PAGE}
        onOpenEditForm={openEditForm}
        onDelete={handleDelete}
      />

      <PaginationComponent
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={handlePageChange}
        isLoading={isLoading}
        hasItems={files.length > 0}
      />

      <FileFormDialog
        isOpen={isFormOpen}
        onOpenChange={setIsFormOpen}
        isEditing={!!editingFileId}
        control={fileControl}
        errors={fileErrors}
        isSubmitting={isFileFormSubmitting}
        onSubmit={onFormSubmit}
        subjectOptions={subjectOptions}
        handleSubmit={handleFileSubmit}
      />

      <DeleteConfirmation
        isOpen={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        nameDelete="Tài liệu"
      />
    </div>
  );
}
