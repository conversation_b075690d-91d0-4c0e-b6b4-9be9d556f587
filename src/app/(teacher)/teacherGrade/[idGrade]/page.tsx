"use client";
import React, { useState, useEffect, useCallback, use, useMemo } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Edit,
  Trash2,
  Save,
  X,
  Users,
  BookOpen,
  Award,
  TrendingUp,
} from "lucide-react";
import { toast } from "sonner";
import {
  CourseData,
  GradeDetail,
  Student,
  StudentGrades,
} from "@/types/teacherGrade";
import StudentForm from "@/components/TeacherGrade/StudentForm";
import GradeTable from "@/components/TeacherGrade/GradeTable";
import StatisticsCard from "@/components/TeacherGrade/StatisticsCard";
import { getAuthToken } from "@/utils/getAuthToken";
import {
  publishGradeAPI,
  updateGradeSheetAPI,
  viewGradeSheetAPI,
} from "@/api/teacher/managerGradeAPI";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface DetailPageProps {
  params: Promise<{
    idGrade: string;
  }>; // Cập nhật kiểu của params
}

function extractGradeValue(grade: number | GradeDetail | null): number {
  if (grade == null) return 0;
  if (typeof grade === "number") return grade;

  const score = grade.score;
  if (score == null) return 0;

  const num = typeof score === "string" ? parseFloat(score) : score;
  return isNaN(num) ? 0 : num;
}

const gradeTypeOptions = [
  {
    value: "midterm_grade",
    label: "Điểm giữa kỳ",
  },
  {
    value: "final_grade",
    label: "Điểm cuối kỳ",
  },
  {
    value: "attendance",
    label: "Chuyên cần",
  },
  {
    value: "practical_grade",
    label: "Điểm thực hành",
  },
  {
    value: "other_grade",
    label: "Điểm khác",
  },
];

export default function DetailGradePage({ params }: DetailPageProps) {
  const { idGrade } = use(params);
  const accessToken = getAuthToken();

  const [listStudent, setListStudent] = useState<Student[]>([]);
  const [courseInfo, setCourseInfo] = useState<any>();
  const [editingStudent, setEditingStudent] = useState<Student | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState<any>();

  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [gradeType, setGradeType] = useState("");

  const fetchListStudent = useCallback(
    async (page = 1, currentFilters = searchTerm) => {
      if (!accessToken) return;
      setIsLoading(true);
      try {
        const response = await viewGradeSheetAPI(
          currentFilters,
          page,
          accessToken,
          idGrade
        );
        const transformedStudents = response.data.students.map(
          (student, index) => ({
            id: String(student.id), // hoặc String(index + 1) nếu bạn muốn ID tuần tự
            name: student.full_name || "",
            studentId: student.code_student || "",
            midterm_grade: student.grades?.midterm_grade ?? 0,
            final_grade: student.grades?.final_grade ?? 0,
            attendance: student.grades?.attendance ?? 0,
            practical_grade: student.grades?.practical_grade ?? 0,
            other_grade: student.grades?.other_grade ?? 0,
            average: calculateAverage(student.grades as StudentGrades),
          })
        );

        console.log(transformedStudents);

        setListStudent(transformedStudents);
        setCourseInfo(response.data.course_info);
        setTotalRecords(response.totalData);
        setTotalPages(Math.ceil(response.totalData / 10));
      } catch (error) {
        console.error("Failed to fetch:", error);
        setListStudent([]);
        setTotalRecords(0);
        setTotalPages(1);
      } finally {
        setIsLoading(false);
      }
    },
    [accessToken, idGrade, searchTerm] // Include filters here
  );

  useEffect(() => {
    fetchListStudent(currentPage, filters);
  }, [fetchListStudent, currentPage, filters]);

  const calculateAverage = (grades: StudentGrades) => {
    const gradeValues = [
      grades.midterm_grade,
      grades.final_grade,
      grades.attendance,
      grades.practical_grade,
      grades.other_grade,
    ].map(extractGradeValue);

    const validGrades = gradeValues.filter(
      (v) => typeof v === "number" && !isNaN(v)
    );

    if (validGrades.length === 0) return 0;

    const sum = validGrades.reduce((total, val) => total + val, 0);
    return Number((sum / validGrades.length).toFixed(2));
  };

  const handleEditStudent = async (
    studentData: Omit<Student, "id" | "average">
  ) => {
    if (!accessToken) return;
    if (!editingStudent) return;

    console.log(studentData);

    const updatedStudent = [
      {
        id_student: editingStudent.id,
        score: studentData.final_grade,
        grade_type: "final_grade",
        weight: 10,
        note: studentData.note_final_grade,
        grade_update_reason: studentData.grade_update_reason_finalterm,
      },
      {
        id_student: editingStudent.id,
        score: studentData.midterm_grade,
        grade_type: "midterm_grade",
        weight: 10,
        note: studentData.note_midterm_grade,
        grade_update_reason: studentData.grade_update_reason_midterm,
      },
      {
        id_student: editingStudent.id,
        score: studentData.attendance,
        grade_type: "attendance",
        weight: 10,
        note: studentData.note_attendance,
        grade_update_reason: studentData.grade_update_reason_attendance,
      },
      {
        id_student: editingStudent.id,
        score: studentData.practical_grade,
        grade_type: "practical_grade",
        weight: 10,
        note: studentData.note_practical_grade,
        grade_update_reason: studentData.grade_update_reason_practical_grade,
      },
      {
        id_student: editingStudent.id,
        score: studentData.other_grade,
        grade_type: "other_grade",
        weight: 10,
        note: studentData.note_other_grade,
        grade_update_reason: studentData.grade_update_reason_other_grade,
      },
    ];

    const res = await updateGradeSheetAPI(accessToken, updatedStudent, idGrade);
    if (res.code === 0) {
      fetchListStudent(currentPage, filters);
      setEditingStudent(null);
      toast.success("Cập nhật học sinh thành công");
    } else {
      toast.error("Cập nhật học sinh thất bại");
    }
  };

  const deleteStudent = (id: string) => {
    toast.info("Xóa học sinh thành công");
  };

  const filteredStudents = listStudent.filter(
    (listStudent) =>
      listStudent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      listStudent.studentId.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const statistics = useMemo(() => {
    const avg =
      totalRecords > 0
        ? Number(
            (
              listStudent.reduce((sum, s) => sum + s.average, 0) / totalRecords
            ).toFixed(2)
          )
        : 0;

    return {
      totalStudents: totalRecords,
      averageGrade: avg,
      highPerformers: listStudent.filter((s) => s.average >= 8.5).length,
      lowPerformers: listStudent.filter((s) => s.average < 5.0).length,
    };
  }, [listStudent, totalRecords]);

  const handlePublish = async () => {
    if (!accessToken) return;
    if (!idGrade) return;
    setIsSubmitting(true);
    const res = await publishGradeAPI(accessToken, idGrade, gradeType);
    if (res.code === 0) {
      fetchListStudent(currentPage, filters);
      setIsDialogOpen(false);
      setGradeType("");
      toast.success("Công bố điểm thành công");
    } else {
      toast.error("Công bố điểm thất bại");
    }
    setIsSubmitting(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8 text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            Hệ thống quản lý điểm danh sách học sinh lớp học phần:{" "}
            {courseInfo?.name}
          </h1>
          <p className="text-lg text-gray-600">
            Quản lý chấm điểm danh sách học sinh lớp học phần
          </p>
        </div>
        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatisticsCard
            title="Tổng số học sinh"
            value={statistics?.totalStudents?.toString() ?? "0"}
            icon={Users}
            color="blue"
          />
          <StatisticsCard
            title="Điểm TB lớp"
            value={statistics.averageGrade.toString()}
            icon={BookOpen}
            color="green"
          />
          <StatisticsCard
            title="Học sinh giỏi"
            value={statistics.highPerformers.toString()}
            icon={Award}
            color="purple"
          />
          <StatisticsCard
            title="Cần cải thiện"
            value={statistics.lowPerformers.toString()}
            icon={TrendingUp}
            color="orange"
          />
        </div>
        {/* Main Content */}
        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="bg-gradient-to-r from-blue-600 to-blue-700 text-white">
            <CardTitle className="flex items-center justify-between py-4">
              <span className="text-2xl font-bold">Danh Sách Học Sinh</span>
            </CardTitle>
          </CardHeader>

          <CardContent className="p-6">
            {/* Search Bar */}
            <div className="mb-6 flex justify-between items-center">
              <Input
                placeholder="Tìm kiếm theo tên hoặc mã số học sinh..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="max-w-md"
              />

              <Button variant="outline" onClick={() => setIsDialogOpen(true)}>
                Công bố điểm
              </Button>
            </div>

            <Tabs defaultValue="table" className="w-full">
              {/* <TabsList className="grid w-full grid-cols-2 max-w-md">
                <TabsTrigger value="table">Bảng điểm</TabsTrigger>
                <TabsTrigger value="statistics">Thống kê</TabsTrigger>
              </TabsList> */}

              <TabsContent value="table" className="mt-6">
                <GradeTable
                  students={filteredStudents}
                  onEdit={setEditingStudent}
                  onDelete={deleteStudent}
                />
              </TabsContent>

              {/* <TabsContent value="statistics" className="mt-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Phân bố điểm số</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {[
                          {
                            range: "9.0 - 10.0",
                            count: students.filter((s) => s.average >= 9.0)
                              .length,
                            color: "bg-green-500",
                          },
                          {
                            range: "8.0 - 8.9",
                            count: students.filter(
                              (s) => s.average >= 8.0 && s.average < 9.0
                            ).length,
                            color: "bg-blue-500",
                          },
                          {
                            range: "7.0 - 7.9",
                            count: students.filter(
                              (s) => s.average >= 7.0 && s.average < 8.0
                            ).length,
                            color: "bg-yellow-500",
                          },
                          {
                            range: "6.0 - 6.9",
                            count: students.filter(
                              (s) => s.average >= 6.0 && s.average < 7.0
                            ).length,
                            color: "bg-orange-500",
                          },
                          {
                            range: "< 6.0",
                            count: students.filter((s) => s.average < 6.0)
                              .length,
                            color: "bg-red-500",
                          },
                        ].map((item) => (
                          <div
                            key={item.range}
                            className="flex items-center justify-between"
                          >
                            <span className="text-sm font-medium">
                              {item.range}
                            </span>
                            <div className="flex items-center space-x-2">
                              <div
                                className={`w-4 h-4 rounded ${item.color}`}
                              ></div>
                              <span className="font-bold">{item.count}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Top 5 học sinh xuất sắc</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {students
                          .sort((a, b) => b.average - a.average)
                          .slice(0, 5)
                          .map((student, index) => (
                            <div
                              key={student.id}
                              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                            >
                              <div className="flex items-center space-x-3">
                                <Badge
                                  variant="secondary"
                                  className="w-6 h-6 rounded-full flex items-center justify-center p-0"
                                >
                                  {index + 1}
                                </Badge>
                                <div>
                                  <p className="font-medium">{student.name}</p>
                                  <p className="text-sm text-gray-500">
                                    {student.studentId}
                                  </p>
                                </div>
                              </div>
                              <Badge
                                variant="default"
                                className="bg-blue-100 text-blue-800"
                              >
                                {student.average}
                              </Badge>
                            </div>
                          ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent> */}
            </Tabs>
          </CardContent>
        </Card>
        {/* Student Form Modal */}
        {editingStudent && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold text-gray-900">
                    Chỉnh sửa học sinh
                  </h2>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setEditingStudent(null)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                <StudentForm
                  student={editingStudent}
                  onSubmit={handleEditStudent}
                  onCancel={() => setEditingStudent(null)}
                />
              </div>
            </div>
          </div>
        )}
        {/* Dialog công bố điểm */}s
        <Dialog
          open={isDialogOpen}
          onOpenChange={(open) => setIsDialogOpen(open)}
        >
          <DialogContent className="sm:max-w-[525px]">
            <DialogHeader>
              <DialogTitle>Công bố điểm</DialogTitle>
              <DialogDescription>
                Công bố loại điểm cho học viên
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <Label>Loại điểm công bố</Label>
              <Select
                value={gradeType}
                onValueChange={(value: string) => {
                  console.log("Selected grade type:", value);
                  setGradeType(value);
                }}
              >
                <SelectTrigger className="w-full" id="grade_type">
                  <SelectValue placeholder="Chọn loại điểm" />
                </SelectTrigger>
                <SelectContent>
                  {gradeTypeOptions.map((item) => (
                    <SelectItem key={item.value} value={item.value}>
                      {item.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <DialogFooter className="pt-4">
              <DialogClose asChild>
                <Button type="button" variant="outline">
                  Hủy
                </Button>
              </DialogClose>
              <Button
                type="submit"
                onClick={handlePublish}
                disabled={isSubmitting}
              >
                Xác nhận
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
