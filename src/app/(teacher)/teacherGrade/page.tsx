"use client";
import {
  addCourseAP<PERSON>,
  deleteCourseAPI,
  listCourceAPI,
} from "@/api/managerCourseAPI";
import { Course, CourseFilter, CourseFormData } from "@/types/managerCourse";
import { getAuthToken } from "@/utils/getAuthToken";
import React, { useCallback, useEffect, useState } from "react";

import { PaginationComponent } from "@/components/Pagination";
import { FiltersCource } from "@/components/TeacherGrade/CourseFilter";
import CoursesTable from "@/components/TeacherGrade/CourseTable";
import { listScheduleAPI } from "@/api/teacher/managerScheduleAPI";
;

const ITEMS_PER_PAGE = 20;

export default function ManagerCoursePage() {
  const accessToken = getAuthToken();

  const [listCource, setListCource] = useState<Course[]>([]);
  const [filters, setFilters] = useState<CourseFilter>({});

  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  const fetchListCource = useCallback(
    async (page = 1, currentFilters = filters) => {
      if (!accessToken) return;
      setIsLoading(true);
      try {
        const response = await listScheduleAPI(accessToken);
        setListCource(response.data);
        setTotalRecords(response.totalData);
        setTotalPages(Math.ceil(response.totalData / ITEMS_PER_PAGE));
      } catch (error) {
        console.error("Failed to fetch registrations:", error);
        setListCource([]);
        setTotalRecords(0);
        setTotalPages(1);
      } finally {
        setIsLoading(false);
      }
    },
    [accessToken, filters] // Include filters here
  );

  useEffect(() => {
    fetchListCource(currentPage, filters);
  }, [fetchListCource, currentPage, filters]);

  const handleFilterChange = (
    filterName: keyof CourseFilter,
    value: string | undefined
  ) => {
    setFilters((prev) => ({ ...prev, [filterName]: value }));
  };

  const handleSearch = () => {
    setCurrentPage(1);
    // fetchRegistrations will be called by useEffect due to filters/currentPage change
  };

  const handleResetFilters = () => {
    setFilters({});
    setCurrentPage(1);
    // fetchRegistrations will be called by useEffect
  };

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages && newPage !== currentPage) {
      setCurrentPage(newPage);
    }
  };

  return (
    <div className="container mx-auto p-4 md:p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold text-gray-800">Quản lý học phần phụ trách</h2>
      </div>
      {/* <FiltersCource
        filters={filters}
        onFilterChange={handleFilterChange}
        onSearch={handleSearch}
        onResetFilters={handleResetFilters}
      /> */}

      <CoursesTable
        courses={listCource}
        isLoading={isLoading}
        totalRecords={totalRecords}
        currentPage={currentPage}
        itemsPerPage={ITEMS_PER_PAGE}
      />

      <PaginationComponent
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={handlePageChange}
        isLoading={isLoading}
        hasItems={listCource.length > 0}
      />
    </div>
  );
}
