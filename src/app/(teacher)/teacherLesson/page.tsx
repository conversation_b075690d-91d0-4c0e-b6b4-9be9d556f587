
"use client";
import React, { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import { toast } from "sonner";
import { getAuthToken } from "@/utils/getAuthToken";

// Components
import { PaginationComponent } from "@/components/Pagination";
import { DeleteConfirmation } from "@/components/DeleteConfirmation";
import { LessonFilters } from "@/components/TeacherLesson/LessonFilter";
import { LessonDetailDialog } from "@/components/TeacherLesson/LessonDetailDialog";

// Dynamic import với ssr: false cho các component sử dụng File API
const LessonTable = dynamic(
  () => import("@/components/TeacherLesson/LessonTable"),
  { ssr: false }
);

const LessonFormDialog = dynamic(
  () => import("@/components/TeacherLesson/LessonFormDialog").then(mod => ({ default: mod.LessonFormDialog })),
  { ssr: false }
);

// Types and APIs
import {
  Lesson,
  LessonFilter,
  LessonFormData,
  CourseOption,
  LESSON_PAGINATION_CONFIG,
} from "@/types/lesson";
import {
  listLessonsAPI,
  addLessonAPI,
  deleteLessonAPI,
} from "@/api/teacher/lessonAPI";
import { listCourceAPI } from "@/api/managerCourseAPI";
import dynamic from "next/dynamic";

const ITEMS_PER_PAGE = LESSON_PAGINATION_CONFIG.defaultLimit;

export default function TeacherLessonPage() {
  const accessToken = getAuthToken();

  // State management
  const [lessons, setLessons] = useState<Lesson[]>([]);
  const [courseOptions, setCourseOptions] = useState<CourseOption[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [totalPages, setTotalPages] = useState(1);

  // Filter state
  const [filters, setFilters] = useState<LessonFilter>({});

  // Dialog states
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDetailOpen, setIsDetailOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Selected items
  const [editingLesson, setEditingLesson] = useState<Lesson | null>(null);
  const [viewingLesson, setViewingLesson] = useState<Lesson | null>(null);
  const [lessonToDeleteId, setLessonToDeleteId] = useState<string | null>(null);

  // Fetch lessons
  const fetchLessons = useCallback(async (page: number = 1) => {
    if (!accessToken) return;

    setIsLoading(true);
    try {
      // Clean filters to match API expectations
      const cleanFilters: any = { ...filters };
      if (cleanFilters.status === "") {
        delete cleanFilters.status;
      }

      const response = await listLessonsAPI(cleanFilters, page, accessToken, ITEMS_PER_PAGE);
      setLessons(response.data);
      setTotalRecords(response.totalData);
      setTotalPages(response.totalPage || Math.ceil(response.totalData / ITEMS_PER_PAGE));
    } catch (error) {
      console.error("Error fetching lessons:", error);
      toast.error(
        `Lỗi khi tải danh sách bài học: ${
          error instanceof Error ? error.message : "Lỗi không xác định"
        }`
      );
    } finally {
      setIsLoading(false);
    }
  }, [accessToken, filters]);

  // Fetch course options
  const fetchCourseOptions = useCallback(async () => {
    if (!accessToken) return;

    try {
      const response = await listCourceAPI({}, 1, accessToken);
      const courses: CourseOption[] = response.data.map((course: any) => ({
        id: course.id,
        name: course.name,
        code: course.code,
        subject_info: course.subject_info,
      }));
      setCourseOptions(courses);
    } catch (error) {
      console.error("Error fetching courses:", error);
      toast.error("Không thể tải danh sách khóa học");
    }
  }, [accessToken]);

  // Initial data fetch
  useEffect(() => {
    if (accessToken) {
      fetchCourseOptions();
      fetchLessons(1);
    }
  }, [accessToken, fetchCourseOptions, fetchLessons]);

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
    fetchLessons(newPage);
  };

  // Handle search
  const handleSearch = () => {
    setCurrentPage(1);
    fetchLessons(1);
  };

  // Handle filter reset
  const handleResetFilters = () => {
    setFilters({});
    setCurrentPage(1);
  };

  // Reset filters effect
  useEffect(() => {
    if (Object.keys(filters).length === 0) {
      fetchLessons(1);
    }
  }, [filters, fetchLessons]);

  // Handle form submission
  const handleFormSubmit = async (data: LessonFormData) => {
    if (!accessToken) return;

    setIsSubmitting(true);
    try {
      const isEdit = !!editingLesson;
      const response = await addLessonAPI(
        data,
        accessToken,
        isEdit,
        editingLesson?.id
      );

      if (response.code === 1) {
        toast.success(
          isEdit ? "Cập nhật bài học thành công!" : "Thêm bài học thành công!"
        );
        setIsFormOpen(false);
        setEditingLesson(null);
        fetchLessons(currentPage);
      } else {
        toast.error(response.mess || "Có lỗi xảy ra");
      }
    } catch (error) {
      console.error("Error submitting lesson:", error);
      toast.error(
        `Lỗi khi ${editingLesson ? "cập nhật" : "thêm"} bài học: ${
          error instanceof Error ? error.message : "Lỗi không xác định"
        }`
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete confirmation
  const handleConfirmDelete = async () => {
    if (!accessToken || !lessonToDeleteId) return;

    setIsLoading(true);
    try {
      const response = await deleteLessonAPI(lessonToDeleteId, accessToken);
      if (response.code === 0) {
        toast.success("Đã xóa bài học thành công!");
        fetchLessons(currentPage);
      } else {
        toast.error("Đã có lỗi xảy ra khi xóa bài học");
      }
    } catch (error) {
      console.error("Failed to delete lesson:", error);
      toast.error(
        `Lỗi khi xóa bài học: ${
          error instanceof Error ? error.message : "Lỗi không xác định"
        }`
      );
    } finally {
      setIsLoading(false);
      setIsDeleteDialogOpen(false);
      setLessonToDeleteId(null);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Quản lý Bài học</h1>
          <p className="text-gray-600 mt-1">
            Quản lý và tổ chức các bài học trong khóa học
          </p>
        </div>
        <Button
          onClick={() => {
            setEditingLesson(null);
            setIsFormOpen(true);
          }}
          className="flex items-center gap-2"
          disabled={isLoading}
        >
          <PlusCircle className="h-4 w-4" />
          Thêm Bài học
        </Button>
      </div>

      {/* Filters */}
      <LessonFilters
        filters={filters}
        onFiltersChange={setFilters}
        onSearch={handleSearch}
        onReset={handleResetFilters}
        courseOptions={courseOptions}
        isLoading={isLoading}
      />

      {/* Lessons Table */}
      <LessonTable
        lessons={lessons}
        courseOptions={courseOptions}
        isLoading={isLoading}
        totalRecords={totalRecords}
        currentPage={currentPage}
        itemsPerPage={ITEMS_PER_PAGE}
        onView={async (lesson) => {
          setViewingLesson(lesson);
          setIsDetailOpen(true);
        }}
        onEdit={(lesson) => {
          setEditingLesson(lesson);
          setIsFormOpen(true);
        }}
        onDelete={(id) => {
          setLessonToDeleteId(id);
          setIsDeleteDialogOpen(true);
        }}
      />

      {/* Pagination */}
      <PaginationComponent
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={handlePageChange}
        isLoading={isLoading}
        hasItems={lessons.length > 0}
      />

      {/* Form Dialog */}
      <LessonFormDialog
        isOpen={isFormOpen}
        onOpenChange={setIsFormOpen}
        isEditing={!!editingLesson}
        onSubmit={handleFormSubmit}
        courseOptions={courseOptions}
        isSubmitting={isSubmitting}
        initialData={editingLesson ? {
          title: editingLesson.title,
          content: editingLesson.content,
          description: editingLesson.description,
          youtube_code: editingLesson.youtube_code,
          link_file_video: editingLesson.link_file_video,
          id_course: editingLesson.id_course,
          status: editingLesson.status,
        } : undefined}
      />

      {/* Detail Dialog */}
      <LessonDetailDialog
        isOpen={isDetailOpen}
        onOpenChange={setIsDetailOpen}
        lesson={viewingLesson}
        onEdit={(lesson) => {
          setIsDetailOpen(false);
          setEditingLesson(lesson);
          setIsFormOpen(true);
        }}
        onDelete={(id) => {
          setIsDetailOpen(false);
          setLessonToDeleteId(id);
          setIsDeleteDialogOpen(true);
        }}
      />

      {/* Delete Confirmation */}
      <DeleteConfirmation
        isOpen={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        nameDelete="bài học"
      />
    </div>
  );
}
