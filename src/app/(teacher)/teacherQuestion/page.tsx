"use client";
import React, { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import { toast } from "sonner";
import { getAuthToken } from "@/utils/getAuthToken";

// Components
import { PaginationComponent } from "@/components/Pagination";
import { DeleteConfirmation } from "@/components/DeleteConfirmation";
import QuestionTable from "@/components/TeacherQuestion/QuestionTable";
import { QuestionFilters } from "@/components/TeacherQuestion/QuestionFilter";
import { QuestionFormDialog } from "@/components/TeacherQuestion/QuestionFormDialog";
import { QuestionDetailDialog } from "@/components/TeacherQuestion/QuestionDetailDialog";

// Types and API
import {
  Question,
  QuestionFilter,
  QuestionFormData,
  QuestionTableState,
  QuestionFormState,
  QuestionDetailState,
} from "@/types/question";
import {
  listQuestionsAPI,
  addQuestionAPI,
  deleteQuestionAPI,
} from "@/api/teacher/questionAPI";

const ITEMS_PER_PAGE = 20;

export default function TeacherQuestionPage() {
  // Table state
  const [tableState, setTableState] = useState<QuestionTableState>({
    questions: [],
    isLoading: false,
    error: null,
    currentPage: 1,
    totalPages: 1,
    totalRecords: 0,
    filters: {},
  });

  // Form state
  const [formState, setFormState] = useState<QuestionFormState>({
    isOpen: false,
    isEdit: false,
    editingQuestion: null,
    isSubmitting: false,
  });

  // Detail state
  const [detailState, setDetailState] = useState<QuestionDetailState>({
    isOpen: false,
    selectedQuestion: null,
    isLoading: false,
  });

  // Delete state
  const [deleteState, setDeleteState] = useState({
    isOpen: false,
    questionId: "",
    questionText: "",
  });

  // Load questions
  const loadQuestions = useCallback(async (page: number = 1, filters: QuestionFilter = {}) => {
    const token = getAuthToken();
    if (!token) {
      toast.error("Không tìm thấy token xác thực");
      return;
    }

    setTableState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await listQuestionsAPI(filters, page, token, ITEMS_PER_PAGE);

      setTableState(prev => ({
        ...prev,
        questions: response.data,
        totalRecords: response.totalData,
        totalPages: response.totalPage || Math.ceil(response.totalData / ITEMS_PER_PAGE),
        currentPage: page,
        filters,
        isLoading: false,
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi tải danh sách câu hỏi";
      setTableState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      toast.error(errorMessage);
    }
  }, []);

  // Initial load
  useEffect(() => {
    loadQuestions();
  }, [loadQuestions]);

  // Handle search
  const handleSearch = useCallback(() => {
    loadQuestions(1, tableState.filters);
  }, [loadQuestions, tableState.filters]);

  // Handle filter reset
  const handleResetFilters = useCallback(() => {
    const resetFilters: QuestionFilter = {};
    setTableState(prev => ({ ...prev, filters: resetFilters }));
    loadQuestions(1, resetFilters);
  }, [loadQuestions]);

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    loadQuestions(page, tableState.filters);
  }, [loadQuestions, tableState.filters]);

  // Handle view question
  const handleViewQuestion = useCallback(async (question: Question) => {
    setDetailState({
      isOpen: true,
      selectedQuestion: question,
      isLoading: false,
    });
  }, []);

  // Handle add question
  const handleAddQuestion = useCallback(() => {
    setFormState({
      isOpen: true,
      isEdit: false,
      editingQuestion: null,
      isSubmitting: false,
    });
  }, []);

  // Handle edit question
  const handleEditQuestion = useCallback((question: Question) => {
    setFormState({
      isOpen: true,
      isEdit: true,
      editingQuestion: question,
      isSubmitting: false,
    });
  }, []);

  // Handle form submit
  const handleFormSubmit = useCallback(async (data: QuestionFormData) => {
    const token = getAuthToken();
    if (!token) {
      toast.error("Không tìm thấy token xác thực");
      return;
    }

    setFormState(prev => ({ ...prev, isSubmitting: true }));

    try {
      await addQuestionAPI(
        data,
        token,
        formState.isEdit,
        formState.editingQuestion?.id
      );

      toast.success(
        formState.isEdit
          ? "Cập nhật câu hỏi thành công!"
          : "Thêm câu hỏi thành công!"
      );

      setFormState({
        isOpen: false,
        isEdit: false,
        editingQuestion: null,
        isSubmitting: false,
      });

      // Reload questions
      loadQuestions(tableState.currentPage, tableState.filters);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra";
      toast.error(errorMessage);
      setFormState(prev => ({ ...prev, isSubmitting: false }));
    }
  }, [formState.isEdit, formState.editingQuestion, loadQuestions, tableState.currentPage, tableState.filters]);

  // Handle delete question
  const handleDeleteQuestion = useCallback(async () => {
    const token = getAuthToken();
    if (!token) {
      toast.error("Không tìm thấy token xác thực");
      return;
    }

    try {
      await deleteQuestionAPI(token, parseInt(deleteState.questionId), true);
      toast.success("Xóa câu hỏi thành công!");

      setDeleteState({
        isOpen: false,
        questionId: "",
        questionText: "",
      });

      // Reload questions
      loadQuestions(tableState.currentPage, tableState.filters);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi xóa câu hỏi";
      toast.error(errorMessage);
    }
  }, [deleteState.questionId, loadQuestions, tableState.currentPage, tableState.filters]);

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Quản lý Câu hỏi</h1>
          <p className="text-gray-600 mt-1">
            Quản lý câu hỏi trắc nghiệm và bộ câu hỏi
          </p>
        </div>
        <Button onClick={handleAddQuestion} className="flex items-center gap-2">
          <PlusCircle className="h-4 w-4" />
          Thêm câu hỏi
        </Button>
      </div>

      {/* Filters */}
      <QuestionFilters
        filters={tableState.filters}
        onFiltersChange={(filters) =>
          setTableState(prev => ({ ...prev, filters }))
        }
        onSearch={handleSearch}
        onReset={handleResetFilters}
        isLoading={tableState.isLoading}
      />

      {/* Table */}
      <QuestionTable
        questions={tableState.questions}
        isLoading={tableState.isLoading}
        totalRecords={tableState.totalRecords}
        currentPage={tableState.currentPage}
        itemsPerPage={ITEMS_PER_PAGE}
        onView={handleViewQuestion}
        onEdit={handleEditQuestion}
        onDelete={(id) =>
          setDeleteState({
            isOpen: true,
            questionId: id,
            questionText: tableState.questions.find(q => q.id === id)?.question || "",
          })
        }
      />

      {/* Pagination */}
      <PaginationComponent
        currentPage={tableState.currentPage}
        totalPages={tableState.totalPages}
        onPageChange={handlePageChange}
        isLoading={tableState.isLoading}
        hasItems={tableState.questions.length > 0}
      />

      {/* Form Dialog */}
      <QuestionFormDialog
        isOpen={formState.isOpen}
        onOpenChange={(isOpen) =>
          setFormState(prev => ({ ...prev, isOpen }))
        }
        isEditing={formState.isEdit}
        onSubmit={handleFormSubmit}
        isSubmitting={formState.isSubmitting}
        initialData={formState.editingQuestion || undefined}
      />

      {/* Detail Dialog */}
      <QuestionDetailDialog
        isOpen={detailState.isOpen}
        onOpenChange={(isOpen) =>
          setDetailState(prev => ({ ...prev, isOpen }))
        }
        question={detailState.selectedQuestion}
        onEdit={handleEditQuestion}
        onDelete={(id) =>
          setDeleteState({
            isOpen: true,
            questionId: id,
            questionText: detailState.selectedQuestion?.question || "",
          })
        }
        isLoading={detailState.isLoading}
      />

      {/* Delete Confirmation */}
      <DeleteConfirmation
        isOpen={deleteState.isOpen}
        onClose={() =>
          setDeleteState({
            isOpen: false,
            questionId: "",
            questionText: "",
          })
        }
        onConfirm={handleDeleteQuestion}
        nameDelete={`câu hỏi "${deleteState.questionText.substring(0, 50)}${deleteState.questionText.length > 50 ? '...' : ''}"`}
      />
    </div>
  );
}
