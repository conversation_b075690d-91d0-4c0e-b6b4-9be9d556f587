"use client"
import { listScheduleAPI } from "@/api/teacher/managerScheduleAPI";
import { getAuthToken } from "@/utils/getAuthToken";
import { useCallback, useEffect, useState } from "react";
import { Calendar, Clock, MapPin, Users, BookOpen, ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";

interface ScheduleSession {
  id: number;
  id_course: number;
  date_of_week: number;
  week_start: string;
  week_end: string;
  period_start: string;
  period_end: string;
  room: string;
  day_name: string;
}

interface Course {
  id: number;
  name: string;
  code: string;
  groups: string;
  id_teacher: number;
  id_semester: number;
  id_subject: number;
  fee: number;
  number_students: number;
  slug: string;
  teacher_info: {
    id: number;
    full_name: string;
    email: string;
    phone: string;
  };
  semester_info: {
    id: number;
    name: string;
    code: string;
    start_date: string;
    end_date: string;
  };
  subject_info: {
    id: number;
    name: string;
    keyword: string;
  };
  schedule_sessions: ScheduleSession[];
}

const daysOfWeek = [
  { id: 2, name: "<PERSON>h<PERSON> hai", shortName: "T2" },
  { id: 3, name: "<PERSON>h<PERSON> ba", shortName: "T3" },
  { id: 4, name: "Thứ tư", shortName: "T4" },
  { id: 5, name: "Thứ năm", shortName: "T5" },
  { id: 6, name: "Thứ sáu", shortName: "T6" },
  { id: 7, name: "Thứ bảy", shortName: "T7" },
  { id: 1, name: "Chủ nhật", shortName: "CN" },
];

const getRandomColor = () => {
  const colors = [
    "bg-blue-100 border-blue-300 text-blue-800",
    "bg-green-100 border-green-300 text-green-800",
    "bg-purple-100 border-purple-300 text-purple-800",
    "bg-orange-100 border-orange-300 text-orange-800",
    "bg-pink-100 border-pink-300 text-pink-800",
    "bg-indigo-100 border-indigo-300 text-indigo-800",
    "bg-teal-100 border-teal-300 text-teal-800",
  ];
  return colors[Math.floor(Math.random() * colors.length)];
};

// Hàm định dạng ngày tháng
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('vi-VN', { 
    day: '2-digit', 
    month: '2-digit', 
    year: 'numeric' 
  });
};

// Hàm tính ngày đầu và cuối tuần từ một ngày bất kỳ
const getWeekRange = (date: Date) => {
  const startOfWeek = new Date(date);
  const day = startOfWeek.getDay(); // 0 = Sunday, 1 = Monday, ...
  const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
  startOfWeek.setDate(diff);
  
  const endOfWeek = new Date(startOfWeek);
  endOfWeek.setDate(startOfWeek.getDate() + 6);
  
  return {
    start: startOfWeek.toISOString().split('T')[0],
    end: endOfWeek.toISOString().split('T')[0]
  };
};

// Hàm tính tuần từ một ngày bất kỳ
const getWeekFromDate = (date: Date) => {
  return getWeekRange(date);
};

export default function TeacherSchedule() {
  const accessToken = getAuthToken();
  const [dataSchedule, setDataSchedule] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [courseColors, setCourseColors] = useState<{[key: number]: string}>({});
  const [currentWeek, setCurrentWeek] = useState<{start: string, end: string}>(() => {
    // Khởi tạo với tuần hiện tại
    return getWeekFromDate(new Date());
  });

  // Hàm lấy dữ liệu lịch học cho một tuần cụ thể
  const fetchDataSchedule = useCallback(async (weekStart: string, weekEnd: string) => {
    try {
      setLoading(true);
      console.log(`Đang tải dữ liệu cho tuần: ${weekStart} - ${weekEnd}`);
      
      const response = await listScheduleAPI(accessToken, weekStart, weekEnd);
      console.log('Response từ API:', response);
      
      setDataSchedule(response.data || []);
      
      // Assign colors to courses
      const colors: {[key: number]: string} = {};
      (response.data || []).forEach((course: Course) => {
        colors[course.id] = getRandomColor();
      });
      setCourseColors(colors);
      
    } catch (error) {
      console.error("Error fetching data:", error);
      setDataSchedule([]);
    } finally {
      setLoading(false);
    }
  }, [accessToken]);

  // Load dữ liệu cho tuần hiện tại khi component mount
  useEffect(() => {
    const today = new Date();
    const currentWeekRange = getWeekFromDate(today);
    console.log('Tuần hiện tại:', currentWeekRange);
    
    setCurrentWeek(currentWeekRange);
    fetchDataSchedule(currentWeekRange.start, currentWeekRange.end);
  }, [fetchDataSchedule]);

  // Chuyển đến tuần trước
  const goToPreviousWeek = () => {
    const prevWeekStart = new Date(currentWeek.start);
    prevWeekStart.setDate(prevWeekStart.getDate() - 7);
    const prevWeek = getWeekFromDate(prevWeekStart);
    
    setCurrentWeek(prevWeek);
    fetchDataSchedule(prevWeek.start, prevWeek.end);
  };

  // Chuyển đến tuần sau
  const goToNextWeek = () => {
    const nextWeekStart = new Date(currentWeek.start);
    nextWeekStart.setDate(nextWeekStart.getDate() + 7);
    const nextWeek = getWeekFromDate(nextWeekStart);
    
    setCurrentWeek(nextWeek);
    fetchDataSchedule(nextWeek.start, nextWeek.end);
  };

  // Về tuần hiện tại
  const goToCurrentWeek = () => {
    const today = new Date();
    const currentWeekRange = getWeekFromDate(today);
    
    setCurrentWeek(currentWeekRange);
    fetchDataSchedule(currentWeekRange.start, currentWeekRange.end);
  };

  // Kiểm tra có phải tuần hiện tại không
  const isCurrentWeek = () => {
    const today = new Date();
    const actualCurrentWeek = getWeekFromDate(today);
    return currentWeek.start === actualCurrentWeek.start && currentWeek.end === actualCurrentWeek.end;
  };

  // Organize schedule by day of week
  const scheduleByDay = daysOfWeek.map(day => {
    const sessions: (ScheduleSession & { course: Course })[] = [];
    
    dataSchedule.forEach(course => {
      course.schedule_sessions.forEach(session => {
        if (session.date_of_week === day.id) {
          sessions.push({ ...session, course });
        }
      });
    });

    // Sort sessions by start time
    sessions.sort((a, b) => {
      const timeA = a.period_start.replace(':', '');
      const timeB = b.period_start.replace(':', '');
      return timeA.localeCompare(timeB);
    });

    return {
      ...day,
      sessions
    };
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải thời khóa biểu...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center gap-3 mb-4">
            <Calendar className="w-8 h-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">Thời khóa biểu giảng dạy</h1>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <BookOpen className="w-4 h-4" />
              <span>Tổng số khóa học: {dataSchedule.length}</span>
            </div>
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              <span>Tổng học viên: {dataSchedule.reduce((sum, course) => sum + course.number_students, 0)}</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4" />
              <span>Cập nhật: {new Date().toLocaleDateString('vi-VN')}</span>
            </div>
          </div>

          {/* Điều khiển chuyển tuần */}
          <div className="mt-4 flex items-center justify-between border-t pt-4">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={goToPreviousWeek}
            >
              <ChevronLeft className="w-4 h-4 mr-1" /> Tuần trước
            </Button>
            
            <div className="flex items-center gap-4">
              <div className="text-center">
                <div className="font-medium text-lg">
                  {formatDate(currentWeek.start)} - {formatDate(currentWeek.end)}
                </div>
                <div className="text-sm text-gray-500">
                  {isCurrentWeek() ? 'Tuần hiện tại' : 'Tuần học'}
                </div>
              </div>
              
              {!isCurrentWeek() && (
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={goToCurrentWeek}
                  className="text-blue-600 border-blue-600 hover:bg-blue-50"
                >
                  Hôm nay
                </Button>
              )}
            </div>
            
            <Button 
              variant="outline" 
              size="sm" 
              onClick={goToNextWeek}
            >
              Tuần sau <ChevronRight className="w-4 h-4 ml-1" />
            </Button>
          </div>
        </div>

        {/* Schedule Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-7 gap-4">
          {scheduleByDay.map(day => (
            <div key={day.id} className="bg-white rounded-lg shadow-sm overflow-hidden">
              {/* Day Header */}
              <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-4">
                <h2 className="font-semibold text-center">{day.name}</h2>
                <p className="text-blue-100 text-sm text-center">{day.shortName}</p>
              </div>

              {/* Sessions */}
              <div className="p-3 space-y-3 min-h-[400px]">
                {day.sessions.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Calendar className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">Không có lịch học</p>
                  </div>
                ) : (
                  day.sessions.map(session => (
                    <div
                      key={session.id}
                      className={`p-3 rounded-lg border-l-4 ${courseColors[session.course.id]} transition-all hover:shadow-md`}
                    >
                      <div className="space-y-2">
                        <h3 className="font-semibold text-sm leading-tight">
                          {session.course.name}
                        </h3>
                        
                        <div className="space-y-1 text-xs">
                          <div className="flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            <span>{session.period_start} - {session.period_end}</span>
                          </div>
                          
                          <div className="flex items-center gap-1">
                            <MapPin className="w-3 h-3" />
                            <span>{session.room}</span>
                          </div>
                          
                          <div className="flex items-center gap-1">
                            <BookOpen className="w-3 h-3" />
                            <span>{session.course.subject_info?.name}</span>
                          </div>
                          
                          <div className="flex items-center gap-1">
                            <Users className="w-3 h-3" />
                            <span>{session.course.number_students} học viên</span>
                          </div>
                        </div>

                        <div className="text-xs text-gray-500 pt-1 border-t border-gray-200">
                          <div>Nhóm: {session.course.groups}</div>
                          <div>Kỳ: {session.course.semester_info.name}</div>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Course Legend */}
        {dataSchedule.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm p-6 mt-6">
            <h3 className="text-lg font-semibold mb-4">Danh sách khóa học</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {dataSchedule.map(course => (
                <div
                  key={course.id}
                  className={`p-4 rounded-lg border-l-4 ${courseColors[course.id]}`}
                >
                  <h4 className="font-semibold mb-2">{course.name}</h4>
                  <div className="text-sm space-y-1">
                    <div>Mã: {course.code}</div>
                    <div>Môn: {course.subject_info?.name}</div>
                    <div>Học viên: {course.number_students}</div>
                    <div>Kỳ học: {course.semester_info.name}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}