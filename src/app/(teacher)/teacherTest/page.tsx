"use client";
import React, { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import { toast } from "sonner";
import { getAuthToken } from "@/utils/getAuthToken";

// Components
import { PaginationComponent } from "@/components/Pagination";
import { DeleteConfirmation } from "@/components/DeleteConfirmation";
import TestTable from "@/components/TeacherTest/TestTable";
import { TestFilters } from "@/components/TeacherTest/TestFilter";
import { TestFormDialog } from "@/components/TeacherTest/TestFormDialog";
import { TestDetailDialog } from "@/components/TeacherTest/TestDetailDialog";

// Types and API
import {
  Test,
  TestFilter,
  TestFormData,
  TestTableState,
  TestFormState,
  TestDetailState,
  LessonOption,
  TEST_PAGINATION_CONFIG,
} from "@/types/test";
import {
  listTestsAPI,
  addTestAPI,
  deleteTestAPI,
  detailTestAPI,
} from "@/api/teacher/testAPI";
import { listLessonsAPI } from "@/api/teacher/lessonAPI";

const ITEMS_PER_PAGE = TEST_PAGINATION_CONFIG.defaultLimit;

export default function TeacherTestPage() {
  // Table state
  const [tableState, setTableState] = useState<TestTableState>({
    tests: [],
    isLoading: false,
    error: null,
    currentPage: 1,
    totalPages: 1,
    totalRecords: 0,
    filters: {},
  });

  // Form state
  const [formState, setFormState] = useState<TestFormState>({
    isOpen: false,
    isEdit: false,
    editingTest: null,
    isSubmitting: false,
  });

  // Detail state
  const [detailState, setDetailState] = useState<TestDetailState>({
    isOpen: false,
    selectedTest: null,
    isLoading: false,
  });

  // Delete state
  const [deleteState, setDeleteState] = useState({
    isOpen: false,
    testId: "",
    testTitle: "",
  });

  // Lesson options for dropdown
  const [lessonOptions, setLessonOptions] = useState<LessonOption[]>([]);

  // Load lessons for dropdown
  const loadLessons = useCallback(async () => {
    const token = getAuthToken();
    if (!token) return;

    try {
      const response = await listLessonsAPI({ status: 'active' }, 1, token, 100);
      const lessons: LessonOption[] = response.data.map(lesson => ({
        id: lesson.id,
        title: lesson.title,
      }));
      setLessonOptions(lessons);
    } catch (error) {
      console.error("Error loading lessons:", error);
      toast.error("Không thể tải danh sách bài học");
    }
  }, []);

  // Load tests
  const loadTests = useCallback(async (page: number = 1, filters: TestFilter = {}) => {
    const token = getAuthToken();
    if (!token) {
      toast.error("Không tìm thấy token xác thực");
      return;
    }

    setTableState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await listTestsAPI(filters, page, token, ITEMS_PER_PAGE);

      setTableState(prev => ({
        ...prev,
        tests: response.data,
        totalRecords: response.totalData,
        totalPages: response.totalPage || Math.ceil(response.totalData / ITEMS_PER_PAGE),
        currentPage: page,
        filters,
        isLoading: false,
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi tải danh sách bài kiểm tra";
      setTableState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      toast.error(errorMessage);
    }
  }, []);

  // Initial load
  useEffect(() => {
    loadTests();
    loadLessons();
  }, [loadTests, loadLessons]);

  // Handle search
  const handleSearch = useCallback(() => {
    loadTests(1, tableState.filters);
  }, [loadTests, tableState.filters]);

  // Handle filter reset
  const handleResetFilters = useCallback(() => {
    const resetFilters: TestFilter = {};
    setTableState(prev => ({ ...prev, filters: resetFilters }));
    loadTests(1, resetFilters);
  }, [loadTests]);

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    loadTests(page, tableState.filters);
  }, [loadTests, tableState.filters]);

  // Handle view test
  const handleViewTest = useCallback(async (test: Test) => {
    const token = getAuthToken();
    if (!token) {
      toast.error("Không tìm thấy token xác thực");
      return;
    }

    setDetailState({
      isOpen: true,
      selectedTest: null,
      isLoading: true,
    });

    try {
      const fullTestData = await detailTestAPI(token, test.id);
      setDetailState({
        isOpen: true,
        selectedTest: fullTestData,
        isLoading: false,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi tải chi tiết bài kiểm tra";
      toast.error(errorMessage);
      setDetailState({
        isOpen: false,
        selectedTest: null,
        isLoading: false,
      });
    }
  }, []);

  // Handle add test
  const handleAddTest = useCallback(() => {
    setFormState({
      isOpen: true,
      isEdit: false,
      editingTest: null,
      isSubmitting: false,
    });
  }, []);

  // Handle edit test
  const handleEditTest = useCallback(async (test: Test) => {
    const token = getAuthToken();
    if (!token) {
      toast.error("Không tìm thấy token xác thực");
      return;
    }

    // Show loading state
    setFormState({
      isOpen: true,
      isEdit: true,
      editingTest: null,
      isSubmitting: true,
    });

    try {
      const fullTestData = await detailTestAPI(token, test.id);
      setFormState({
        isOpen: true,
        isEdit: true,
        editingTest: fullTestData,
        isSubmitting: false,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi tải chi tiết bài kiểm tra";
      toast.error(errorMessage);
      setFormState({
        isOpen: false,
        isEdit: false,
        editingTest: null,
        isSubmitting: false,
      });
    }
  }, []);

  // Handle form submit
  const handleFormSubmit = useCallback(async (data: TestFormData) => {
    console.log("handleFormSubmit called with data:", data);

    const token = getAuthToken();
    if (!token) {
      console.log("No token found");
      toast.error("Không tìm thấy token xác thực");
      return;
    }

    console.log("Setting isSubmitting to true");
    setFormState(prev => ({ ...prev, isSubmitting: true }));

    try {
      await addTestAPI(
        data,
        token,
        formState.isEdit,
        formState.editingTest?.id
      );

      toast.success(
        formState.isEdit
          ? "Cập nhật bài kiểm tra thành công!"
          : "Thêm bài kiểm tra thành công!"
      );

      setFormState({
        isOpen: false,
        isEdit: false,
        editingTest: null,
        isSubmitting: false,
      });

      // Reload tests
      loadTests(tableState.currentPage, tableState.filters);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra";
      toast.error(errorMessage);
      setFormState(prev => ({ ...prev, isSubmitting: false }));
    }
  }, [formState.isEdit, formState.editingTest, loadTests, tableState.currentPage, tableState.filters]);

  // Handle delete test
  const handleDeleteTest = useCallback(async () => {
    const token = getAuthToken();
    if (!token) {
      toast.error("Không tìm thấy token xác thực");
      return;
    }

    try {
      await deleteTestAPI(token, deleteState.testId);
      toast.success("Xóa bài kiểm tra thành công!");

      setDeleteState({
        isOpen: false,
        testId: "",
        testTitle: "",
      });

      // Reload tests
      loadTests(tableState.currentPage, tableState.filters);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi xóa bài kiểm tra";
      toast.error(errorMessage);
    }
  }, [deleteState.testId, loadTests, tableState.currentPage, tableState.filters]);

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Quản lý Bài kiểm tra</h1>
          <p className="text-gray-600 mt-1">
            Quản lý bài kiểm tra trắc nghiệm và câu hỏi
          </p>
        </div>
        <Button onClick={handleAddTest} className="flex items-center gap-2">
          <PlusCircle className="h-4 w-4" />
          Thêm bài kiểm tra
        </Button>
      </div>

      {/* Filters */}
      <TestFilters
        filters={tableState.filters}
        onFiltersChange={(filters) =>
          setTableState(prev => ({ ...prev, filters }))
        }
        onSearch={handleSearch}
        onReset={handleResetFilters}
        isLoading={tableState.isLoading}
        lessonOptions={lessonOptions}
      />

      {/* Table */}
      <TestTable
        tests={tableState.tests}
        isLoading={tableState.isLoading}
        totalRecords={tableState.totalRecords}
        currentPage={tableState.currentPage}
        itemsPerPage={ITEMS_PER_PAGE}
        lessonOptions={lessonOptions}
        onView={handleViewTest}
        onEdit={handleEditTest}
        onDelete={(id) =>
          setDeleteState({
            isOpen: true,
            testId: id,
            testTitle: tableState.tests.find(t => t.id === id)?.title || "",
          })
        }
      />

      {/* Pagination */}
      <PaginationComponent
        currentPage={tableState.currentPage}
        totalPages={tableState.totalPages}
        onPageChange={handlePageChange}
        isLoading={tableState.isLoading}
        hasItems={tableState.tests.length > 0}
      />

      {/* Form Dialog */}
      <TestFormDialog
        isOpen={formState.isOpen}
        onOpenChange={(isOpen) =>
          setFormState(prev => ({ ...prev, isOpen }))
        }
        isEditing={formState.isEdit}
        onSubmit={handleFormSubmit}
        isSubmitting={formState.isSubmitting}
        initialData={formState.editingTest || undefined}
        testId={formState.editingTest?.id}
        testTitle={formState.editingTest?.title}
        lessonOptions={lessonOptions}
      />

      {/* Detail Dialog */}
      <TestDetailDialog
        isOpen={detailState.isOpen}
        onOpenChange={(isOpen) =>
          setDetailState(prev => ({ ...prev, isOpen }))
        }
        test={detailState.selectedTest}
        lessonOptions={lessonOptions}
        onEdit={handleEditTest}
        onDelete={(id) =>
          setDeleteState({
            isOpen: true,
            testId: id,
            testTitle: detailState.selectedTest?.title || "",
          })
        }
        isLoading={detailState.isLoading}
      />

      {/* Delete Confirmation */}
      <DeleteConfirmation
        isOpen={deleteState.isOpen}
        onClose={() =>
          setDeleteState({
            isOpen: false,
            testId: "",
            testTitle: "",
          })
        }
        onConfirm={handleDeleteTest}
        nameDelete={`bài kiểm tra "${deleteState.testTitle}"`}
      />
    </div>
  );
}
