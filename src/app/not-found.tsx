import React from 'react';
import Link from 'next/link'; // Sử dụng Link của Next.js để điều hướng

const NotFound = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100 text-center p-6">
      <h1 className="text-6xl font-bold text-blue-600 mb-4">404</h1>
      <h2 className="text-3xl font-semibold text-gray-800 mb-2">Trang không tìm thấy</h2>
      <p className="text-gray-600 mb-8">
        <PERSON>n lỗi, chúng tôi không thể tìm thấy trang bạn đang tìm kiếm.
        Có vẻ như trang này đã bị xóa, đổi tên hoặc không tồn tại.
      </p>
      <Link href="/dashboardTeacher">
        <div className="px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg shadow-md hover:bg-blue-700 transition duration-300">
          Về trang chủ
        </div>
      </Link>
    </div>
  );
};

export default NotFound;