import React, { useState, useRef } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Upload, X } from "lucide-react";
import { useDispatch } from 'react-redux';
import { updateAvatar } from '@/redux/features/user/userSlice';

interface LogoUploadProps {
  onLogoChange: (file: File | null) => void;
  error?: string;
}

const LogoUpload = ({ onLogoChange, error }: LogoUploadProps) => {
  const [preview, setPreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dispatch = useDispatch();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    onLogoChange(file);
    
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        const result = reader.result as string;
        setPreview(result);
        dispatch(updateAvatar(result)); // Dispatch action để cập nhật avatar trong Redux store
      };
      reader.readAsDataURL(file);
    } else {
      setPreview(null);
      dispatch(updateAvatar('/avatar.jpg')); // Reset về avatar mặc định
    }
  };

  const handleRemoveImage = () => {
    setPreview(null);
    onLogoChange(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <div className="mb-6">
      <p className="block text-sm font-medium mb-2 text-gray-700">
        School Logo
      </p>
      <div
        className={cn(
          "border-2 border-dashed rounded-lg p-4 flex flex-col items-center justify-center",
          error ? "border-red-500" : "border-gray-300 hover:border-blue-500",
          "transition-colors duration-200"
        )}
      >
        {preview ? (
          <div className="relative w-full">
            <img
              src={preview}
              alt="Logo Preview"
              className="mx-auto h-32 object-contain rounded"
            />
            <button
              type="button"
              onClick={handleRemoveImage}
              className="absolute top-0 right-0 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
              aria-label="Remove image"
            >
              <X size={16} />
            </button>
          </div>
        ) : (
          <div className="text-center">
            <Upload className="mx-auto h-12 w-12 text-gray-400" />
            <div className="mt-2">
              <p className="text-sm text-gray-500">
                Click to upload or drag and drop
              </p>
              <p className="text-xs text-gray-500">PNG, JPG, GIF up to 2MB</p>
            </div>
          </div>
        )}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          className={preview ? "hidden" : "absolute inset-0 h-[10%] opacity-0 cursor-pointer m-[200px]"}
          onChange={handleFileChange}
        />
        {!preview && (
          <Button 
            type="button" 
            variant="outline" 
            className="mt-2" 
            onClick={() => fileInputRef.current?.click()}
          >
            Select File
          </Button>
        )}
      </div>
      {error && <p className="mt-1 text-sm text-red-500">{error}</p>}
    </div>
  );
};

export default LogoUpload;