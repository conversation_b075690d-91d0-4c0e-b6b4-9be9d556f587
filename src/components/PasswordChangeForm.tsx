'use client'
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { toast } from 'sonner';
import { Eye, EyeOff, Loader2 } from 'lucide-react';
import API from '@/lib/axios';
import { getAuthToken } from "@/utils/getAuthToken";

// Create schema for password change form
const passwordChangeSchema = z.object({
  current_password: z.string().min(6, 'Mật khẩu hiện tại phải có ít nhất 6 ký tự'),
  new_password: z.string().min(6, '<PERSON><PERSON><PERSON> khẩu mới phải có ít nhất 6 ký tự'),
  password_confirmation: z.string().min(6, '<PERSON><PERSON><PERSON> nhận mật khẩu phải có ít nhất 6 ký tự')
}).refine((data) => data.new_password === data.password_confirmation, {
  message: "Mật khẩu xác nhận không khớp",
  path: ["password_confirmation"]
});

type PasswordChangeFormValues = z.infer<typeof passwordChangeSchema>;

const PasswordChangeForm = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  // Initialize form with react-hook-form and zod validation
  const form = useForm<PasswordChangeFormValues>({
    resolver: zodResolver(passwordChangeSchema),
    defaultValues: {
      current_password: '',
      new_password: '',
      password_confirmation: ''
    }
  });

  // Handle form submission
  const onSubmit = async (data: PasswordChangeFormValues) => {
    setIsLoading(true);
    
    try {
      const token = getAuthToken();
      if (!token) {
        toast.error('Không tìm thấy token đăng nhập');
        setIsLoading(false);
        return;
      }
      
      const response = await API.post('/changePasswordMemberAPI', {
        token: token,
        current_password: data.current_password,
        new_password: data.new_password,
        password_confirmation: data.password_confirmation
      });
      
      // Xử lý các mã trạng thái trả về từ API
      switch(response.data.code) {
        case 1:
          toast.success('Lưu mật khẩu mới thành công');
          form.reset();
          break;
        case 2:
          toast.error('Tài khoản không tồn tại hoặc sai token');
          break;
        case 3:
          toast.error('Sai số điện thoại');
          break;
        case 4:
          toast.error('Mã xác thực nhập không đúng');
          break;
        case 5:
          toast.error('Mật khẩu nhập lại không đúng');
          break;
        default:
          toast.error('Có lỗi xảy ra khi thay đổi mật khẩu');
      }
    } catch (error) {
      console.error('Error changing password:', error);
      toast.error('Đã xảy ra lỗi khi thay đổi mật khẩu');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardContent className="pt-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="current_password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">
                    Mật khẩu hiện tại <span className="text-red-500">*</span>
                  </FormLabel>
                  <div className="relative">
                    <FormControl>
                      <Input 
                        type={showCurrentPassword ? "text" : "password"}
                        placeholder="Nhập mật khẩu hiện tại" 
                        {...field} 
                      />
                    </FormControl>
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 flex items-center px-3"
                      onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                    >
                      {showCurrentPassword ? 
                        <EyeOff className="h-4 w-4 text-gray-500" /> : 
                        <Eye className="h-4 w-4 text-gray-500" />
                      }
                    </button>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="new_password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">
                    Mật khẩu mới <span className="text-red-500">*</span>
                  </FormLabel>
                  <div className="relative">
                    <FormControl>
                      <Input 
                        type={showNewPassword ? "text" : "password"}
                        placeholder="Nhập mật khẩu mới" 
                        {...field} 
                      />
                    </FormControl>
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 flex items-center px-3"
                      onClick={() => setShowNewPassword(!showNewPassword)}
                    >
                      {showNewPassword ? 
                        <EyeOff className="h-4 w-4 text-gray-500" /> : 
                        <Eye className="h-4 w-4 text-gray-500" />
                      }
                    </button>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password_confirmation"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">
                    Xác nhận mật khẩu mới <span className="text-red-500">*</span>
                  </FormLabel>
                  <div className="relative">
                    <FormControl>
                      <Input 
                        type={showConfirmPassword ? "text" : "password"}
                        placeholder="Xác nhận mật khẩu mới" 
                        {...field} 
                      />
                    </FormControl>
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 flex items-center px-3"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    >
                      {showConfirmPassword ? 
                        <EyeOff className="h-4 w-4 text-gray-500" /> : 
                        <Eye className="h-4 w-4 text-gray-500" />
                      }
                    </button>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end pt-4">
              <Button 
                type="submit" 
                disabled={isLoading}
                className="min-w-[120px]"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Đang xử lý
                  </>
                ) : 'Lưu thay đổi'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default PasswordChangeForm;