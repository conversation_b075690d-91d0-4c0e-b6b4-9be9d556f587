import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>le,
  CardDescription,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { RotateCcw, Search } from "lucide-react";
import { StudentFilter } from "@/types/managerclass";

interface CardProps {
  filters: StudentFilter;
  onFilterChange: (
    SourceName: keyof StudentFilter,
    value: string | undefined
  ) => void;
  onSearch: () => void;
  onResetFilters: () => void;
}

export function FiltersStudent({
  filters,
  onFilterChange,
  onSearch,
  onResetFilters,
}: CardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Bộ lọc tìm kiếm</CardTitle>
        <CardDescription>Tìm kiếm và lọc danh sách học viên.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Input
            placeholder="Tên học viên..."
            value={filters.student_name || ""}
            onChange={(e) => onFilterChange("student_name", e.target.value)}
          />

          <Input
            placeholder="Mã học viên..."
            value={filters.student_code || ""}
            onChange={(e) => onFilterChange("student_code", e.target.value)}
          />

        </div>
        <div className="flex justify-end space-x-2 pt-2">
          <Button variant="outline" onClick={onResetFilters}>
            <RotateCcw className="mr-2 h-4 w-4" /> Đặt lại
          </Button>
          <Button onClick={onSearch}>
            <Search className="mr-2 h-4 w-4" /> Tìm kiếm
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
