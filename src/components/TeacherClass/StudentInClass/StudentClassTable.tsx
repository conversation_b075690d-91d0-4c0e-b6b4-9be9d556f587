// Bảng ql phòng ban
import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import {Student } from "@/types/managerclass";

interface StudentClassProps {
  students: Student[];
  isLoading: boolean;
  totalRecords: number;
  currentPage: number;
  itemsPerPage: number;
  onOpenDetail: (id: string) => void;
}

export default function StudentClassTable({
  students,
  isLoading,
  totalRecords,
  currentPage,
  itemsPerPage,
  onOpenDetail,
}: StudentClassProps) {
  return (
    <div>
      <Card>
        <CardHeader>
          <CardTitle>Danh sách học viên trong lớp</CardTitle>
          <CardDescription>
            Đang có tổng cộng {totalRecords} học viên.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <p className="text-center py-8">Đang tải dữ liệu...</p>
          ) : students.length === 0 ? (
            <p className="text-center py-8">
              Không tìm thấy học viên nào phù hợp.
            </p>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-blue-300 text-white! hover:bg-blue-300">
                    <TableHead>STT</TableHead>
                    <TableHead>Tên học viên</TableHead>
                    <TableHead>MSSV</TableHead>
                    <TableHead>Số điện thoại</TableHead>
                    <TableHead className="text-right">Hành động</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {students.map((reg, index) => (
                    <TableRow key={reg.id}>
                      <TableCell>
                        {(currentPage - 1) * itemsPerPage + index + 1}
                      </TableCell>

                      <TableCell>{reg.full_name}</TableCell>
                      <TableCell>{reg.code_student || "N/A"}</TableCell>
                      <TableCell>{reg.phone || "N/A"}</TableCell>

                      <TableCell className="text-right space-x-1">
                        <Button
                          className="cursor-pointer"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            onOpenDetail(reg.id);
                          }}
                        >
                          Xem
                        </Button>
                       
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
