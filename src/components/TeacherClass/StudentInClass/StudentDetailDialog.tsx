import React from "react";
import {
  Di<PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Student, StudentFormData } from "@/types/managerStudent";

interface StudentDialogProps {
  student: Student;
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
}

export function StudentDetailDialog({
  student,
  isOpen,
  onOpenChange,
}: StudentDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle>Thông tin học viên</DialogTitle>
        </DialogHeader>

        <div className="max-h-[70vh] overflow-y-auto p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Avatar */}
              <div className="flex items-center gap-4 col-span-full">
                <div className="w-24 h-24 rounded-full overflow-hidden border-2 border-gray-300">
                  <img
                    src={
                      student.avatar || "https://www.gravatar.com/avatar/?d=mp"
                    }
                    alt="Ảnh đại diện"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">
                    Ảnh đại diện
                  </Label>
                </div>
              </div>

              {/* Họ và tên */}
              <div>
                <Label className="text-sm text-muted-foreground">
                  Họ và tên
                </Label>
                <p className="text-base font-semibold text-gray-900 mt-1">
                  {student.full_name}
                </p>
              </div>

              {/* Mã học viên */}
              <div>
                <Label className="text-sm text-muted-foreground">
                  Mã học viên
                </Label>
                <p className="text-base font-semibold text-gray-900 mt-1">
                  {student.code_student || "Chưa cập nhật"}
                </p>
              </div>

              {/* Email */}
              <div>
                <Label className="text-sm text-muted-foreground">Email</Label>
                <p className="text-base font-semibold text-gray-900 mt-1">
                  {student.email || "Chưa cập nhật"}
                </p>
              </div>

              {/* Số điện thoại */}
              <div>
                <Label className="text-sm text-muted-foreground">
                  Số điện thoại
                </Label>
                <p className="text-base font-semibold text-gray-900 mt-1">
                  {student.phone || "Chưa cập nhật"}
                </p>
              </div>

              {/* Địa chỉ */}
              <div className="md:col-span-2">
                <Label className="text-sm text-muted-foreground">Địa chỉ</Label>
                <p className="text-base font-semibold text-gray-900 mt-1">
                  {student.address || "Chưa cập nhật"}
                </p>
              </div>

              {/* Giới tính */}
              <div>
                <Label className="text-sm text-muted-foreground">
                  Giới tính
                </Label>
                <p className="text-base font-semibold text-gray-900 mt-1">
                  {student.gender === "male" ? "Nam" : "Nữ"}
                </p>
              </div>
            </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
