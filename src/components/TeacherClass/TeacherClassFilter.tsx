import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>le,
  CardDescription,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { RotateCcw, Search } from "lucide-react";
import { ClassFilter } from "@/types/managerclass";
import { AcademicYear, Category } from "@/types/category";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";

interface CardProps {
  filters: ClassFilter;
  onFilterChange: (
    SourceName: keyof ClassFilter,
    value: string | undefined
  ) => void;
  onSearch: () => void;
  onResetFilters: () => void;
  listAcademicYear: AcademicYear[];
  listMajor: Category[];
}

export function FiltersClass({
  filters,
  onFilterChange,
  onSearch,
  onResetFilters,
  listAcademicYear,
  listMajor,
}: CardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle><PERSON><PERSON> lọc tìm kiếm</CardTitle>
        <CardDescription>Tìm kiếm và lọc danh sách lớp học.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Input
            placeholder="Tên lớp..."
            value={filters.name || ""}
            onChange={(e) => onFilterChange("name", e.target.value)}
          />

          <Select
            value={filters.id_academic || "all"}
            onValueChange={(value) =>
              onFilterChange("id_academic", value === "all" ? undefined : value)
            }
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Kỳ học" />
            </SelectTrigger>

            <SelectContent>
              <SelectItem value="all">Tất cả</SelectItem>
              {listAcademicYear.map((item) => (
                <SelectItem key={item.id} value={item.id.toString()}>
                  {item.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={filters.id_major || "all"}
            onValueChange={(value) =>
              onFilterChange("id_major", value === "all" ? undefined : value)
            }
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Chuyên ngành" />
            </SelectTrigger>

            <SelectContent>
              <SelectItem value="all">Tất cả</SelectItem>
              {listMajor.map((item) => (
                <SelectItem key={item.id} value={item.id}>
                  {item.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex justify-end space-x-2 pt-2">
          <Button variant="outline" onClick={onResetFilters}>
            <RotateCcw className="mr-2 h-4 w-4" /> Đặt lại
          </Button>
          <Button onClick={onSearch}>
            <Search className="mr-2 h-4 w-4" /> Tìm kiếm
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
