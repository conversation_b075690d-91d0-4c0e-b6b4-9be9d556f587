// Bảng ql phòng ban
import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { ClassRoom } from "@/types/managerclass";
import { useRouter } from "next/navigation";

interface TeacherClassProps {
  classes: ClassRoom[];
  isLoading: boolean;
  totalRecords: number;
  currentPage: number;
  itemsPerPage: number;
}

export default function TeacherClassTable({
  classes,
  isLoading,
  totalRecords,
  currentPage,
  itemsPerPage,
}: TeacherClassProps) {
   const router = useRouter()
  return (
    <div>
      <Card>
        <CardHeader>
          <CardTitle>Danh sách lớp học</CardTitle>
          <CardDescription>
            Đang có tổng cộng {totalRecords} lớp học.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <p className="text-center py-8">Đang tải dữ liệu...</p>
          ) : classes.length === 0 ? (
            <p className="text-center py-8">
              Không tìm thấy lớp học nào phù hợp.
            </p>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-blue-300 text-white! hover:bg-blue-300">
                    <TableHead>STT</TableHead>
                    <TableHead>Tên lớp học</TableHead>
                    <TableHead>Niên khóa</TableHead>
                    <TableHead>Chuyên ngành</TableHead>
                    <TableHead className="text-right">Hành động</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {classes.map((reg, index) => (
                    <TableRow key={reg.id}>
                      <TableCell>
                        {(currentPage - 1) * itemsPerPage + index + 1}
                      </TableCell>

                      <TableCell>{reg.name}</TableCell>
                      <TableCell>{reg.academic?.name || "N/A"}</TableCell>
                      <TableCell>{reg.major?.name || "N/A"}</TableCell>

                      <TableCell className="text-right space-x-1">
                        <Button
                          className="cursor-pointer"
                          variant="outline"
                          size="sm"
                         onClick={() => router.push(`/teacherClass/${reg.id}`)}
                        >
                          Xem Ds Học viên
                        </Button>
                       
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
