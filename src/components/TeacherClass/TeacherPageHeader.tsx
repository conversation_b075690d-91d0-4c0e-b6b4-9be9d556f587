import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";

interface PageHeaderProps {
  name: string;
  isAdd: boolean;
  onOpenAddForm?: () => void;
}

export function PageHeader({ name, isAdd, onOpenAddForm }: PageHeaderProps) {
  return (
    <div className="flex justify-between items-center">
      <h1 className="text-xl md:text-2xl font-bold">Qu<PERSON><PERSON> lý {name}</h1>
      {isAdd && (
        <Button onClick={onOpenAddForm}>
          <PlusCircle className="mr-2 h-4 w-4" /> Thêm {name}
        </Button>
      )}
    </div>
  );
}
