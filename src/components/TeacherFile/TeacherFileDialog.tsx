import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useForm, Controller, SubmitHandler, Control } from "react-hook-form";
import { X } from "lucide-react";
import { DocumentFormData } from "@/types/managerFile";
import { Category } from "@/types/category";
import { Textarea } from "@/components/ui/textarea";

interface StatusOption {
  value: string;
  label: string;
}

interface FileFormDialogProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  isEditing: boolean;
  control: Control<DocumentFormData, any>;
  errors: any;
  isSubmitting: boolean;
  onSubmit: Submit<PERSON><PERSON>ler<DocumentFormData>;
  subjectOptions: Category[];
  handleSubmit: any; // from useForm
}

export function FileFormDialog({
  isOpen,
  onOpenChange,
  isEditing,
  control,
  errors,
  isSubmitting,
  onSubmit,
  subjectOptions,
  handleSubmit,
}: FileFormDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Chỉnh sửa Tài liệu" : "Thêm Tài liệu Mới"}
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? "Cập nhật thông tin tài liệu môn học."
              : "Điền thông tin để tạo tài liệu mới."}
          </DialogDescription>
        </DialogHeader>
        <div className="max-h-[70vh] overflow-y-auto pr-4">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 py-2">
            <div>
              <Label htmlFor="name" className="mb-1">
                Tên tài liệu
              </Label>
              <Controller
                name="name"
                control={control}
                render={({ field }) => (
                  <Input id="name" {...field} placeholder="Tài liệu A" />
                )}
              />
              {errors.name && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.name.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="link_file" className="mb-1">
                File tài liệu
              </Label>
              <Controller
                name="link_file"
                control={control}
                render={({ field }) => {
                  const fileName: any = field.value;
                  return (
                    <div>
                      <Input
                        type="file"
                        id="link_file"
                        onChange={(e) =>
                          field.onChange(
                            e.target.files ? e.target.files[0] : null
                          )
                        }
                      />
                    </div>
                  );
                }}
              />
              {errors.link_file && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.link_file.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="id_subject" className="mb-1">
                Môn học
              </Label>
              <Controller
                name="id_subject"
                control={control}
                render={({ field }) => (
                  <Select
                    onValueChange={field.onChange}
                    value={String(field.value)}
                  >
                    <SelectTrigger className="w-full" id="id_subject">
                      <SelectValue placeholder="Chọn môn học" />
                    </SelectTrigger>
                    <SelectContent>
                      {subjectOptions.map((opt) => (
                        <SelectItem key={opt.id} value={String(opt.id)}>
                          {opt.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.id_subject && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.id_subject.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor="note" className="mb-1">
                Ghi chú
              </Label>
              <Controller
                name="note"
                control={control}
                render={({ field }) => (
                  <Textarea
                    rows={4}
                    id="note"
                    {...field}
                    placeholder="Ghi chú"
                  />
                )}
              />
              {errors.note && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.note.message}
                </p>
              )}
            </div>

            <DialogFooter className="pt-4">
              <DialogClose asChild>
                <Button type="button" variant="outline">
                  Hủy
                </Button>
              </DialogClose>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting
                  ? "Đang lưu..."
                  : isEditing
                  ? "Cập nhật"
                  : "Thêm mới"}
              </Button>
            </DialogFooter>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
