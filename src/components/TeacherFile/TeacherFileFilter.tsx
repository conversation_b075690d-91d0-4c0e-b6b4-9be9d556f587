import React from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON><PERSON>er,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { RotateCcw, Search } from "lucide-react";
import { DocumentFilter } from "@/types/managerFile";
import { Category } from "@/types/category";

interface StatusOption {
  value: string;
  label: string;
}

interface FiltersCardProps {
  filters: DocumentFilter;
  subjectOptions: Category[];
  onFilterChange: (
    filterName: keyof DocumentFilter,
    value: string | undefined
  ) => void;
  onSearch: () => void;
  onResetFilters: () => void;
}

export function FiltersCard({
  filters,
  subjectOptions,
  onFilterChange,
  onSearch,
  onResetFilters,
}: FiltersCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Bộ lọc tìm kiếm</CardTitle>
        <CardDescription>
          Tìm kiếm và lọc danh sách tài liệu.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Input
            placeholder="Tên tài liệu..."
            value={filters.name || ""}
            onChange={(e) => onFilterChange("name", e.target.value)}
          />

          <Select
            value={filters.id_subject || ""}
            onValueChange={(value) =>
              onFilterChange("id_subject", value === "0" ? undefined : value)
            }
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Môn học" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="0">Tất cả môn học</SelectItem>
              {subjectOptions.map((opt) => (
                <SelectItem key={opt.id} value={opt.id}>
                  {opt.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex justify-end space-x-2 pt-2">
          <Button variant="outline" onClick={onResetFilters}>
            <RotateCcw className="mr-2 h-4 w-4" /> Đặt lại
          </Button>
          <Button onClick={onSearch}>
            <Search className="mr-2 h-4 w-4" /> Tìm kiếm
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}