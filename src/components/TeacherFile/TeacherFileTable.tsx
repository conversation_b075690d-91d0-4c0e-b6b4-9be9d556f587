import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { formatDate } from "@/lib/extensions";
import { Paperclip } from "lucide-react";
import {
  TooltipProvider,
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from "@radix-ui/react-tooltip";
import { DocumentItem } from "@/types/managerFile";

interface StatusOption {
  value: string;
  label: string;
}

const getTypeFromExtension = (link: string): string => {
  const extension = link.split('.').pop()?.toLowerCase() || '';
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension)) return 'image';
  if (['pdf'].includes(extension)) return 'pdf';
  if (['doc', 'docx'].includes(extension)) return 'word';
  if (['xls', 'xlsx'].includes(extension)) return 'excel';
  if (['ppt', 'pptx'].includes(extension)) return 'powerpoint';
  return 'other';
};

const getTypeClass = (link: string): string => {
  const type = getTypeFromExtension(link);
  switch (type) {
    case 'image':
      return 'bg-blue-100 text-blue-700';
    case 'pdf':
      return 'bg-red-100 text-red-700';
    case 'word':
      return 'bg-yellow-100 text-yellow-700';
    case 'excel':
      return 'bg-green-100 text-green-700';
    case 'powerpoint':
      return 'bg-purple-100 text-purple-700';
    default:
      return 'bg-gray-100 text-gray-700';
  }
};

const getTypeLabel = (link: string): string => {
  const type = getTypeFromExtension(link);
  switch (type) {
    case 'image':
      return 'Ảnh';
    case 'pdf':
      return 'PDF';
    case 'word':
      return 'Word';
    case 'excel':
      return 'Excel';
    case 'powerpoint':
      return 'PowerPoint';
    default:
      return 'Khác';
  }
};


interface FileTableProps {
  files: DocumentItem[];
  isLoading: boolean;
  totalRecords: number;
  currentPage: number;
  itemsPerPage: number;
  onOpenEditForm: (id: string) => void;
  onDelete: (id: string) => void;
}

export default function FileTable({
  files,
  isLoading,
  totalRecords,
  currentPage,
  itemsPerPage,
  onOpenEditForm,
  onDelete,
}: FileTableProps) {
  return (
    <div>
      <Card>
        <CardHeader>
          <CardTitle>Danh sách Tài liệu</CardTitle>
          <CardDescription>
            Đang có tổng cộng {totalRecords} Tài liệu.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <p className="text-center py-8">Đang tải dữ liệu...</p>
          ) : files.length === 0 ? (
            <p className="text-center py-8">
              Không tìm thấy tài liệu nào phù hợp.
            </p>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-blue-300 text-white! hover:bg-blue-300">
                    <TableHead>STT</TableHead>
                    <TableHead>Tên file</TableHead>
                    <TableHead>Link file</TableHead>
                    <TableHead>Ngày tạo</TableHead>
                    <TableHead>Kiểu file</TableHead>
                    <TableHead>Môn học</TableHead>
                    <TableHead className="text-right">Hành động</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {files.map((reg, index) => (
                    <TableRow key={reg.id}>
                      <TableCell>
                        {(currentPage - 1) * itemsPerPage + index + 1}
                      </TableCell>
                      <TableCell>{reg.name}</TableCell>
                      <TableCell>
                        <div className="max-w-xs lg:max-w-sm overflow-hidden text-ellipsis whitespace-nowrap">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <a
                                  href={reg.link_file}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-600 hover:underline"
                                >
                                  {reg.link_file}
                                </a>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{reg.link_file}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </TableCell>
                      <TableCell>{formatDate(reg.created_at)}</TableCell>
                      <TableCell>
                        <span
                          className={`px-2 py-1 text-xs font-semibold rounded-full ${getTypeClass(
                            reg.link_file
                          )}`}
                        >
                          {getTypeLabel(reg.link_file)}
                        </span>
                      </TableCell>
                      <TableCell>{reg.subject_info?.name}</TableCell>

                      <TableCell className="text-right space-x-1">
                        <Button
                          className="cursor-pointer"
                          variant="outline"
                          size="sm"
                          onClick={() => onOpenEditForm(reg.id)}
                        >
                          Sửa
                        </Button>
                        <Button
                          className="cursor-pointer"
                          variant="destructive"
                          size="sm"
                          onClick={() => onDelete(reg.id)}
                        >
                          Xóa
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
