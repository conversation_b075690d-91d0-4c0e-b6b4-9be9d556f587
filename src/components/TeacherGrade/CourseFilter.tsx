import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { RotateCcw, Search } from "lucide-react";
import { CourseFilter } from "@/types/managerCourse";

interface CourcesCardProps {
  filters: CourseFilter;
  onFilterChange: (
    SourceName: keyof CourseFilter,
    value: string | undefined
  ) => void;
  onSearch: () => void;
  onResetFilters: () => void;
}

export function FiltersCource({
  filters,
  onFilterChange,
  onSearch,
  onResetFilters,
}: CourcesCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Bộ lọc tìm kiếm</CardTitle>
        <CardDescription>Tìm kiếm và lọc danh sách học phần.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Input
            placeholder="Tên học phần..."
            value={filters.name || ""}
            onChange={(e) => onFilterChange("name", e.target.value)}
          />

          <Input
            placeholder="Mã học phần..."
            value={filters.code || ""}
            onChange={(e) => onFilterChange("code", e.target.value)}
          />
        </div>
        <div className="flex justify-end space-x-2 pt-2">
          <Button variant="outline" onClick={onResetFilters}>
            <RotateCcw className="mr-2 h-4 w-4" /> Đặt lại
          </Button>
          <Button onClick={onSearch}>
            <Search className="mr-2 h-4 w-4" /> Tìm kiếm
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
