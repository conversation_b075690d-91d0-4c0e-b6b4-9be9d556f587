
import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Course } from "@/types/managerCourse";
import { useRouter } from "next/navigation";

interface CourseTableProps {
  courses: Course[];
  isLoading: boolean;
  totalRecords: number;
  currentPage: number;
  itemsPerPage: number;
}

export default function CoursesTable({
  courses,
  isLoading,
  totalRecords,
  currentPage,
  itemsPerPage,
}: CourseTableProps) {
  const router = useRouter()
  return (
    <div>
      <Card>
        <CardHeader>
          <CardTitle>Danh sách Học phần</CardTitle>
          <CardDescription>
            Đang có tổng cộng {totalRecords} Học phần.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <p className="text-center py-8"><PERSON><PERSON> tải dữ liệu...</p>
          ) : courses.length === 0 ? (
            <p className="text-center py-8">
              Không tìm thấy học phần nào phù hợp.
            </p>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-blue-300 text-white! hover:bg-blue-300">
                    <TableHead>STT</TableHead>
                    <TableHead>Mã học phần</TableHead>
                    <TableHead>Tên học phần</TableHead>
                    <TableHead className="text-right">Hành động</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {courses.map((reg, index) => (
                    <TableRow key={reg.id}>
                      <TableCell>
                        {(currentPage - 1) * itemsPerPage + index + 1}
                      </TableCell>
                      <TableCell>{reg.code}</TableCell>
                      <TableCell>{reg.name}</TableCell>


                      <TableCell className="text-right space-x-1">
                        <Button
                          className="cursor-pointer"
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/teacherGrade/${reg.id}`)}
                        >
                          Chi tiết
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
