import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Save, X } from "lucide-react";
import { GradeDetail, Student } from "@/types/teacherGrade";
import { Textarea } from "../ui/textarea";

function gradeToString(grade: number | GradeDetail | null): string {
  if (grade == null) return "";
  return typeof grade === "number"
    ? grade.toString()
    : grade.score?.toString() || "";
}

function noteToString(grade: number | GradeDetail | null): string {
  if (grade == null) return "";
  return typeof grade === "number" ? "" : grade.note?.toString() || "";
}

function reasonToString(grade: number | GradeDetail | null): string {
  if (grade == null) return "";
  return typeof grade === "number"
    ? ""
    : grade.grade_update_reason?.toString() || "";
}

interface StudentFormProps {
  student?: Student | null;
  onSubmit: (student: Omit<Student, "id" | "average">) => void;
  onCancel: () => void;
}

const StudentForm: React.FC<StudentFormProps> = ({
  student,
  onSubmit,
  onCancel,
}) => {
  const [formData, setFormData] = useState({
    name: "",
    studentId: "",
    midterm_grade: "",
    final_grade: "",
    attendance: "",
    practical_grade: "",
    other_grade: "",
    note_midterm_grade: "",
    note_final_grade: "",
    note_attendance: "",
    note_practical_grade: "",
    note_other_grade: "",
    grade_update_reason_midterm: "",
    grade_update_reason_finalterm: "",
    grade_update_reason_attendance: "",
    grade_update_reason_practical_grade: "",
    grade_update_reason_other_grade: "",
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (student) {
      setFormData({
        name: student.name,
        studentId: student.studentId,
        midterm_grade: gradeToString(student.midterm_grade),
        final_grade: gradeToString(student.final_grade),
        attendance: gradeToString(student.attendance),
        practical_grade: gradeToString(student.practical_grade),
        other_grade: gradeToString(student.other_grade),
        note_midterm_grade: noteToString(student.midterm_grade),
        note_final_grade: noteToString(student.final_grade),
        note_attendance: noteToString(student.attendance),
        note_practical_grade: noteToString(student.practical_grade),
        note_other_grade: noteToString(student.other_grade),
        grade_update_reason_midterm: reasonToString(student.midterm_grade),
        grade_update_reason_finalterm: reasonToString(student.final_grade),
        grade_update_reason_attendance: reasonToString(student.attendance),
        grade_update_reason_practical_grade: reasonToString(
          student.practical_grade
        ),
        grade_update_reason_other_grade: reasonToString(student.other_grade),
      });
    }
  }, [student]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Validate grades (0-10 range)
    const gradeFields = [
      "midterm_grade",
      "final_grade",
      "attendance",
      "practical_grade",
      "other_grade",
    ];
    gradeFields.forEach((field) => {
      if (formData[field as keyof typeof formData] !== "") {
        const value = parseFloat(formData[field as keyof typeof formData]);
        if (isNaN(value) || value < 0 || value > 10) {
          newErrors[field] = "Điểm phải là số từ 0 đến 10";
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const studentData = {
      ...(student && { id: student.id }),
      name: formData.name.trim(),
      studentId: formData.studentId.trim(),
      midterm_grade: formData.midterm_grade
        ? parseFloat(formData.midterm_grade)
        : null,
      final_grade: formData.final_grade
        ? parseFloat(formData.final_grade)
        : null,
      attendance: formData.attendance ? parseFloat(formData.attendance) : null,
      practical_grade: formData.practical_grade
        ? parseFloat(formData.practical_grade)
        : null,
      other_grade: formData.other_grade
        ? parseFloat(formData.other_grade)
        : null,
      note_midterm_grade: formData.note_midterm_grade,
      note_final_grade: formData.note_final_grade,
      note_attendance: formData.note_attendance,
      note_practical_grade: formData.note_practical_grade,
      note_other_grade: formData.note_other_grade,
      grade_update_reason_midterm: formData.grade_update_reason_midterm,
      grade_update_reason_finalterm: formData.grade_update_reason_finalterm,
      grade_update_reason_attendance: formData.grade_update_reason_attendance,
      grade_update_reason_practical_grade:
        formData.grade_update_reason_practical_grade,
      grade_update_reason_other_grade: formData.grade_update_reason_other_grade,
    };

    onSubmit(studentData);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardContent className="p-4">
          <h3 className="text-lg font-semibold mb-4 text-gray-800">
            Thông tin cơ bản
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label
                htmlFor="name"
                className="text-sm font-medium text-gray-700"
              >
                Họ và tên
              </Label>
              <p>{student?.name}</p>
            </div>

            <div>
              <Label
                htmlFor="studentId"
                className="text-sm font-medium text-gray-700"
              >
                Mã số học sinh
              </Label>
              <p>{student?.studentId || "N/A"}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Grades */}
      <Card>
        <CardContent className="p-4">
          <h3 className="text-lg font-semibold mb-4 text-gray-800">
            Điểm số (0-10)
          </h3>
          <div className="space-y-4">
            {/* Điểm giữa kỳ */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label
                  htmlFor="midterm_grade"
                  className="text-sm font-medium text-gray-700"
                >
                  Điểm giữa kỳ
                </Label>
                <Input
                  id="midterm_grade"
                  type="number"
                  step="0.1"
                  min="0"
                  max="10"
                  value={formData.midterm_grade}
                  onChange={(e) =>
                    handleInputChange("midterm_grade", e.target.value)
                  }
                  placeholder="0.0"
                  className={errors.midterm_grade ? "border-red-500" : ""}
                />
                {errors.midterm_grade && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.midterm_grade}
                  </p>
                )}
              </div>
              <div>
                <Label
                  htmlFor="note_midterm_grade"
                  className="text-sm font-medium text-gray-700"
                >
                  Nhận xét điểm giữa kỳ
                </Label>
                <Input
                  id="note_midterm_grade"
                  type="text"
                  value={formData.note_midterm_grade}
                  onChange={(e) =>
                    handleInputChange("note_midterm_grade", e.target.value)
                  }
                  placeholder="Nhập nhận xét..."
                />
              </div>
            </div>

            <div className="">
              <div>
                <Label
                  htmlFor="grade_update_reason_midterm"
                  className="text-sm font-medium text-gray-700"
                >
                  Lý do khi chỉnh sửa điểm giữa kỳ
                </Label>
                <Textarea
                  id="grade_update_reason_midterm"
                  rows={2}
                  value={formData.grade_update_reason_midterm}
                  onChange={(e) =>
                    handleInputChange(
                      "grade_update_reason_midterm",
                      e.target.value
                    )
                  }
                  placeholder="Nhập lý do..."
                />
              </div>
            </div>

            {/* Điểm cuối kỳ */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label
                  htmlFor="final_grade"
                  className="text-sm font-medium text-gray-700"
                >
                  Điểm cuối kỳ
                </Label>
                <Input
                  id="final_grade"
                  type="number"
                  step="0.1"
                  min="0"
                  max="10"
                  value={formData.final_grade}
                  onChange={(e) =>
                    handleInputChange("final_grade", e.target.value)
                  }
                  placeholder="0.0"
                  className={errors.final_grade ? "border-red-500" : ""}
                />
                {errors.final_grade && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.final_grade}
                  </p>
                )}
              </div>
              <div>
                <Label
                  htmlFor="note_final_grade"
                  className="text-sm font-medium text-gray-700"
                >
                  Nhận xét điểm cuối kỳ
                </Label>
                <Input
                  id="note_final_grade"
                  type="text"
                  value={formData.note_final_grade}
                  onChange={(e) =>
                    handleInputChange("note_final_grade", e.target.value)
                  }
                  placeholder="Nhập nhận xét..."
                />
              </div>
            </div>

            <div className="">
              <div>
                <Label
                  htmlFor="grade_update_reason_finalterm"
                  className="text-sm font-medium text-gray-700"
                >
                  Lý do khi chỉnh sửa điểm cuối kỳ
                </Label>
                <Textarea
                  id="grade_update_reason_finalterm"
                  rows={2}
                  value={formData.grade_update_reason_finalterm}
                  onChange={(e) =>
                    handleInputChange(
                      "grade_update_reason_finalterm",
                      e.target.value
                    )
                  }
                  placeholder="Nhập lý do..."
                />
              </div>
            </div>

            {/* Chuyên cần */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label
                  htmlFor="attendance"
                  className="text-sm font-medium text-gray-700"
                >
                  Chuyên cần
                </Label>
                <Input
                  id="attendance"
                  type="number"
                  step="0.1"
                  min="0"
                  max="10"
                  value={formData.attendance}
                  onChange={(e) =>
                    handleInputChange("attendance", e.target.value)
                  }
                  placeholder="0.0"
                  className={errors.attendance ? "border-red-500" : ""}
                />
                {errors.attendance && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.attendance}
                  </p>
                )}
              </div>
              <div>
                <Label
                  htmlFor="note_attendance"
                  className="text-sm font-medium text-gray-700"
                >
                  Nhận xét chuyên cần
                </Label>
                <Input
                  id="note_attendance"
                  type="text"
                  value={formData.note_attendance}
                  onChange={(e) =>
                    handleInputChange("note_attendance", e.target.value)
                  }
                  placeholder="Nhập nhận xét..."
                />
              </div>
            </div>

            <div className="">
              <div>
                <Label
                  htmlFor="grade_update_reason_attendance"
                  className="text-sm font-medium text-gray-700"
                >
                  Lý do khi chỉnh sửa điểm chuyên cần
                </Label>
                <Textarea
                  id="grade_update_reason_attendance"
                  rows={2}
                  value={formData.grade_update_reason_attendance}
                  onChange={(e) =>
                    handleInputChange(
                      "grade_update_reason_attendance",
                      e.target.value
                    )
                  }
                  placeholder="Nhập lý do..."
                />
              </div>
            </div>

            {/* Điểm thực hành */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label
                  htmlFor="practical_grade"
                  className="text-sm font-medium text-gray-700"
                >
                  Điểm thực hành
                </Label>
                <Input
                  id="practical_grade"
                  type="number"
                  step="0.1"
                  min="0"
                  max="10"
                  value={formData.practical_grade}
                  onChange={(e) =>
                    handleInputChange("practical_grade", e.target.value)
                  }
                  placeholder="0.0"
                  className={errors.practical_grade ? "border-red-500" : ""}
                />
                {errors.practical_grade && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.practical_grade}
                  </p>
                )}
              </div>
              <div>
                <Label
                  htmlFor="note_practical_grade"
                  className="text-sm font-medium text-gray-700"
                >
                  Nhận xét điểm thực hành
                </Label>
                <Input
                  id="note_practical_grade"
                  type="text"
                  value={formData.note_practical_grade}
                  onChange={(e) =>
                    handleInputChange("note_practical_grade", e.target.value)
                  }
                  placeholder="Nhập nhận xét..."
                />
              </div>
            </div>

             <div className="">
              <div>
                <Label
                  htmlFor="grade_update_reason_attendance"
                  className="text-sm font-medium text-gray-700"
                >
                  Lý do khi chỉnh sửa điểm thực hành
                </Label>
                <Textarea
                  id="grade_update_reason_practical_grade"
                  rows={2}
                  value={formData.grade_update_reason_practical_grade}
                  onChange={(e) =>
                    handleInputChange(
                      "grade_update_reason_practical_grade",
                      e.target.value
                    )
                  }
                  placeholder="Nhập lý do..."
                />
              </div>
            </div>

            {/* Điểm khác */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label
                  htmlFor="other_grade"
                  className="text-sm font-medium text-gray-700"
                >
                  Điểm khác
                </Label>
                <Input
                  id="other_grade"
                  type="number"
                  step="0.1"
                  min="0"
                  max="10"
                  value={formData.other_grade}
                  onChange={(e) =>
                    handleInputChange("other_grade", e.target.value)
                  }
                  placeholder="0.0"
                  className={errors.other_grade ? "border-red-500" : ""}
                />
                {errors.other_grade && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.other_grade}
                  </p>
                )}
              </div>
              <div>
                <Label
                  htmlFor="note_other_grade"
                  className="text-sm font-medium text-gray-700"
                >
                  Nhận xét điểm khác
                </Label>
                <Input
                  id="note_other_grade"
                  type="text"
                  value={formData.note_other_grade}
                  onChange={(e) =>
                    handleInputChange("note_other_grade", e.target.value)
                  }
                  placeholder="Nhập nhận xét..."
                />
              </div>
            </div>

            <div className="">
              <div>
                <Label
                  htmlFor="grade_update_reason_attendance"
                  className="text-sm font-medium text-gray-700"
                >
                  Lý do khi chỉnh sửa điểm khác
                </Label>
                <Textarea
                  id="grade_update_reason_other_grade"
                  rows={2}
                  value={formData.grade_update_reason_other_grade}
                  onChange={(e) =>
                    handleInputChange(
                      "grade_update_reason_other_grade",
                      e.target.value
                    )
                  }
                  placeholder="Nhập lý do..."
                />
              </div>
            </div>

            
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 pt-4 border-t">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          className="px-6"
        >
          <X className="mr-2 h-4 w-4" />
          Hủy
        </Button>
        <Button type="submit" className="bg-blue-600 hover:bg-blue-700 px-6">
          <Save className="mr-2 h-4 w-4" />
          {student ? "Cập nhật" : "Thêm mới"}
        </Button>
      </div>
    </form>
  );
};

export default StudentForm;
