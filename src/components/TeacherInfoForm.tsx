"use client";
import React, { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { toast } from "sonner";
// import FormField from "@/components/teacher/FormField";
import LogoUpload from "@/components/LogoUpload";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import API from "@/lib/axios";
import { getAuthToken } from "@/utils/getAuthToken";
import { Bank } from "@/types/category";
import { getListBank } from "@/api/categoryAPI";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const formSchema = z.object({
  full_name: z.string().min(1, "Họ và tên không được để trống."),
  email: z.string().email("Vui lòng nhập email hợp lệ."),
  phone: z
    .string()
    .regex(/^\d{10,11}$/, "Vui lòng nhập số điện thoại hợp lệ (10-11 chữ số)."),
  avatar: z.string().optional(),
  address: z.string().optional(),
});

type FormData = z.infer<typeof formSchema>;

const TeacherInfoForm = () => {
  const token = getAuthToken();
  // Xóa dòng const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      full_name: "",
      email: "",
      phone: "",
      avatar: "",
      address: "",
    },
  });

  // Fetch user data on mount using getInfoMyMemberAPI
  useEffect(() => {
    fetchUserData();
  }, []);

  // Function to fetch user data from API
  const fetchUserData = async () => {
    setIsLoading(true);
    try {
      // Thêm log để kiểm tra token
      console.log("Token being sent:", token);

      const res = await API.post("/getInfoMyMemberAPI", {
        token: token,
      });

      // Thêm log để kiểm tra response
      console.log("Raw API Response:", res);

      if (res.data.code === 1 && res.data.data) {
        const result = res.data.data;
        // Log data trước khi set state
        console.log("Processed user data:", result);

        form.reset({
          full_name: result.full_name || "",
          email: result.email || "",
          phone: result.phone || "",
          avatar: result.avatar || "",
          address: result.address || "",
        });
      } else {
        // Log lỗi chi tiết hơn
        console.error("API Error Response:", res.data);
        toast.error(
          `Lỗi: ${res.data.mess || "Không thể tải thông tin người dùng"}`
        );
      }
    } catch (error: any) {
      // Log chi tiết lỗi
      console.error("Detailed API Error:", {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
      });

      toast.error(
        `Lỗi kết nối: ${
          error.response?.data?.mess ||
          error.message ||
          "Vui lòng kiểm tra kết nối mạng"
        }`
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleAvatarChange = (file: File | null) => {
    setAvatarFile(file);
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        form.setValue("avatar", reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      form.setValue("avatar", "");
    }
  };

  const onSubmit = async (values: FormData) => {
    setIsSubmitting(true);

    try {
      const formData = new FormData();
      formData.append("full_name", values.full_name);
      formData.append("email", values.email);
      formData.append("phone", values.phone);
      formData.append("address", values.address || "");
      formData.append("token", token || "");
      if (avatarFile) {
        formData.append("avatar", avatarFile);
      }

      console.log("Sending data to API:", formData);

      // Call saveInfoMemberMemberAPI
      // Thay vì sử dụng URL trực tiếp, nên sử dụng endpoint tương đối
      const response = await API.post("/saveInfoMemberMemberAPI", formData, {
        headers: {
          "Content-Type": "multipart/form-data", // Đặt Content-Type cho FormData
        },
      });

      // Check result
      if (response.data.code === 1) {
        // Show success message
        toast.success(
          response.data.mess || "Thông tin của bạn đã được cập nhật thành công."
        );

        // Refresh user data after successful update
        fetchUserData();
      } else {
        // Show error message
        toast.error(
          response.data.mess ||
            "Không thể cập nhật thông tin. Vui lòng thử lại."
        );
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("Không thể cập nhật thông tin. Vui lòng thử lại.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Display loading state while fetching initial data
  if (isLoading) {
    return (
      <Card className="shadow-md w-full">
        <CardContent className="py-12 text-center">
          <p>Đang tải thông tin người dùng...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-md w-full">
      <CardContent className="p-6">
        <div className="flex items-center justify-center mb-6">
          <div className="text-center">
            <Avatar className="w-24 h-24 mx-auto mb-2">
              {form.watch("avatar") ? (
                <AvatarImage
                  src={form.watch("avatar")}
                  alt={form.watch("full_name")}
                />
              ) : (
                <AvatarFallback className="text-lg bg-blue-100 text-blue-600">
                  {form.watch("full_name").charAt(0).toUpperCase()}
                </AvatarFallback>
              )}
            </Avatar>
            <LogoUpload onLogoChange={handleAvatarChange} />
          </div>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="full_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Họ và tên</FormLabel>
                    <FormControl>
                      <Input placeholder="Nhập tên của bạn" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Số điện thoại</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Nhập số điện thoại của bạn"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Địa chỉ Email</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="Nhập địa chỉ email của bạn"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Địa chỉ</FormLabel>
                    <FormControl>
                      <Input placeholder="Nhập địa chỉ của bạn" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex justify-end mt-6">
              <Button
                type="submit"
                className="bg-blue-600 hover:bg-blue-700 min-w-[120px]"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Đang lưu..." : "Lưu thay đổi"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default TeacherInfoForm;
