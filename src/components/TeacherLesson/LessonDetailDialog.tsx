import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  Video,
  Youtube,
  Calendar,
  BookOpen,
  FileText,
  ExternalLink,
  Edit,
  Trash2,
} from "lucide-react";
import { Lesson } from "@/types/lesson";

interface LessonDetailDialogProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  lesson: Lesson | null;
  onEdit?: (lesson: Lesson) => void;
  onDelete?: (id: string) => void;
  isLoading?: boolean;
}

export function LessonDetailDialog({
  isOpen,
  onOpenChange,
  lesson,
  onEdit,
  onDelete,
  isLoading = false,
}: LessonDetailDialogProps) {
  if (!lesson) return null;

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString("vi-VN", {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch {
      return "N/A";
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge className="bg-green-100 text-green-800 border-green-200">
            Hoạt động
          </Badge>
        );
      case "inactive":
        return (
          <Badge variant="secondary" className="bg-gray-100 text-gray-800 border-gray-200">
            Không hoạt động
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getYouTubeEmbedUrl = (youtubeCode: string) => {
    // Extract video ID from various YouTube URL formats
    const videoIdMatch = youtubeCode.match(
      /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/
    );
    const videoId = videoIdMatch ? videoIdMatch[1] : youtubeCode;
    return `https://www.youtube.com/embed/${videoId}`;
  };

  const openVideoLink = (url: string) => {
    window.open(url, "_blank", "noopener,noreferrer");
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Chi tiết Bài học
          </DialogTitle>
          <DialogDescription>
            Thông tin chi tiết về bài học và nội dung liên quan.
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2">Đang tải...</span>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Header Info */}
            <div className="space-y-4">
              <div>
                <h3 className="text-xl font-semibold text-gray-900">
                  {lesson.title}
                </h3>
                <div className="flex items-center gap-4 mt-2">
                  {getStatusBadge(lesson.status)}
                  
                </div>
              </div>

              {/* Course Information */}
              {lesson.course_info && (
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">Thông tin khóa học</h4>
                  <div className="space-y-1">
                    <p className="text-sm">
                      <span className="font-medium">Tên khóa học:</span> {lesson.course_info.name}
                    </p>
                    <p className="text-sm">
                      <span className="font-medium">Mã khóa học:</span> {lesson.course_info.code}
                    </p>
                    {lesson.course_info.subject_info && (
                      <p className="text-sm">
                        <span className="font-medium">Môn học:</span> {lesson.course_info.subject_info.name}
                      </p>
                    )}
                  </div>
                </div>
              )}
            </div>

            <Separator />

            {/* Content */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Nội dung bài học
              </h4>
              <div className="prose prose-sm max-w-none">
                <div className="whitespace-pre-wrap text-gray-700 bg-gray-50 p-4 rounded-lg">
                  {lesson.content}
                </div>
              </div>
            </div>

            {/* Description */}
            {lesson.description && (
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Mô tả</h4>
                <div className="text-gray-700 bg-gray-50 p-4 rounded-lg">
                  {lesson.description}
                </div>
              </div>
            )}

            <Separator />

            {/* Video Content */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                <Video className="h-4 w-4" />
                Nội dung video
              </h4>

              {lesson.youtube_code ? (
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Youtube className="h-4 w-4 text-red-500" />
                    <span>Video YouTube</span>
                  </div>
                  <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden">
                    <iframe
                      src={getYouTubeEmbedUrl(lesson.youtube_code)}
                      title="YouTube video player"
                      frameBorder="0"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                      className="w-full h-full"
                    ></iframe>
                  </div>
                </div>
              ) : lesson.link_file_video ? (
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Video className="h-4 w-4 text-blue-500" />
                    <span>File video</span>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Video className="h-8 w-8 text-blue-500" />
                        <div>
                          <p className="font-medium text-gray-900">Video file</p>
                          <p className="text-sm text-gray-500">
                            {typeof lesson.link_file_video === "string" 
                              ? lesson.link_file_video.split('/').pop() 
                              : "Video file"}
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openVideoLink(lesson.link_file_video as string)}
                        className="flex items-center gap-2"
                      >
                        <ExternalLink className="h-4 w-4" />
                        Xem video
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <Video className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                  <p>Chưa có video nào được thêm vào bài học này</p>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            {(onEdit || onDelete) && (
              <>
                <Separator />
                <div className="flex justify-end gap-2">
                  {onEdit && (
                    <Button
                      variant="outline"
                      onClick={() => onEdit(lesson)}
                      className="flex items-center gap-2"
                    >
                      <Edit className="h-4 w-4" />
                      Chỉnh sửa
                    </Button>
                  )}
                  {onDelete && (
                    <Button
                      variant="destructive"
                      onClick={() => onDelete(lesson.id)}
                      className="flex items-center gap-2"
                    >
                      <Trash2 className="h-4 w-4" />
                      Xóa
                    </Button>
                  )}
                </div>
              </>
            )}

            
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
