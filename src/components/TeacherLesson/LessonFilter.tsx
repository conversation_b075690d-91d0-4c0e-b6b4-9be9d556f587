import React from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Search, RotateCcw } from "lucide-react";
import { LessonFilter, CourseOption } from "@/types/lesson";

interface LessonFilterProps {
  filters: LessonFilter;
  onFiltersChange: (filters: LessonFilter) => void;
  onSearch: () => void;
  onReset: () => void;
  courseOptions: CourseOption[];
  isLoading?: boolean;
}

export function LessonFilters({
  filters,
  onFiltersChange,
  onSearch,
  onReset,
  courseOptions,
  isLoading = false,
}: LessonFilterProps) {
  const handleInputChange = (field: keyof LessonFilter, value: string) => {
    onFiltersChange({
      ...filters,
      [field]: value,
    });
  };

  const handleSelectChange = (field: keyof LessonFilter, value: string) => {
    onFiltersChange({
      ...filters,
      [field]: value === "all" ? "" : value,
    });
  };

  const handleReset = () => {
    onReset();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      onSearch();
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Bộ lọc tìm kiếm</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Search Input */}
          <div className="space-y-2">
            <Label htmlFor="search">Tìm kiếm</Label>
            <Input
              id="search"
              placeholder="Tìm theo tiêu đề, nội dung..."
              value={filters.search || ""}
              onChange={(e) => handleInputChange("search", e.target.value)}
              onKeyPress={handleKeyPress}
              className="w-full"
            />
          </div>

          {/* Course Filter */}
          <div className="space-y-2">
            <Label htmlFor="course">Học phần</Label>
            <Select
              value={filters.id_course || "all"}
              onValueChange={(value) => handleSelectChange("id_course", value)}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Chọn học phần" />
              </SelectTrigger>
              <SelectContent className="w-full">
                <SelectItem value="all">Tất cả học phần</SelectItem>
                {courseOptions.map((course) => (
                  <SelectItem key={course.id} value={course.id}>
                    {course.name} ({course.code})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Status Filter */}
          <div className="space-y-2">
            <Label htmlFor="status">Trạng thái</Label>
            <Select
              value={filters.status || "all"}
              onValueChange={(value) => handleSelectChange("status", value)}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Chọn trạng thái" />
              </SelectTrigger>
              <SelectContent className="w-full">
                <SelectItem value="all">Tất cả trạng thái</SelectItem>
                <SelectItem value="active">Hoạt động</SelectItem>
                <SelectItem value="inactive">Không hoạt động</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Subject Name Filter */}
          <div className="space-y-2">
            <Label htmlFor="subject">Môn học</Label>
            <Input
              id="subject"
              placeholder="Tên môn học..."
              value={filters.subject_name || ""}
              onChange={(e) => handleInputChange("subject_name", e.target.value)}
              onKeyPress={handleKeyPress}
              className="w-full"
            />
          </div>

          {/* Faculty Name Filter */}
          <div className="space-y-2">
            <Label htmlFor="faculty">Khoa</Label>
            <Input
              id="faculty"
              placeholder="Tên khoa..."
              value={filters.faculty_name || ""}
              onChange={(e) => handleInputChange("faculty_name", e.target.value)}
              onKeyPress={handleKeyPress}
              className="w-full"
            />
          </div>

          {/* Major Name Filter */}
          <div className="space-y-2">
            <Label htmlFor="major">Chuyên ngành</Label>
            <Input
              id="major"
              placeholder="Tên chuyên ngành..."
              value={filters.major_name || ""}
              onChange={(e) => handleInputChange("major_name", e.target.value)}
              onKeyPress={handleKeyPress}
              className="w-full"
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-2 mt-6">
          <Button
            onClick={onSearch}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <Search className="h-4 w-4" />
            {isLoading ? "Đang tìm kiếm..." : "Tìm kiếm"}
          </Button>
          <Button
            variant="outline"
            onClick={handleReset}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            Đặt lại
          </Button>
        </div>

      </CardContent>
    </Card>
  );
}
