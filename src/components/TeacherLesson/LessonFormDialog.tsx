import React, { useState, useRef } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useForm, Controller, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Upload, X, Video, Link, Youtube } from "lucide-react";
import { LessonFormData, CourseOption, LESSON_STATUS_OPTIONS } from "@/types/lesson";
import { validateVideoFile, formatFileSize, extractYouTubeVideoId } from "@/api/teacher/lessonAPI";

// Validation schema
const lessonSchema = z.object({
  title: z.string().min(1, "Tiêu đề là bắt buộc").max(255, "Tiêu đề không được quá 255 ký tự"),
  content: z.string().min(1, "Nội dung là bắt buộc"),
  description: z.string().optional(),
  youtube_code: z.string().optional(),
  link_file_video: z.union([z.string(), z.instanceof(File)]).optional(),
  id_course: z.string().min(1, "Vui lòng chọn khóa học"),
  status: z.enum(["active", "inactive"]),
});

interface LessonFormDialogProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  isEditing: boolean;
  onSubmit: SubmitHandler<LessonFormData>;
  courseOptions: CourseOption[];
  isSubmitting: boolean;
  initialData?: Partial<LessonFormData>;
}

export function LessonFormDialog({
  isOpen,
  onOpenChange,
  isEditing,
  onSubmit,
  courseOptions,
  isSubmitting,
  initialData,
}: LessonFormDialogProps) {
  const [videoFile, setVideoFile] = useState<File | null>(null);
  const [videoPreview, setVideoPreview] = useState<string | null>(null);
  const [videoType, setVideoType] = useState<"file" | "youtube" | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = useForm<LessonFormData>({
    resolver: zodResolver(lessonSchema),
    defaultValues: {
      title: "",
      content: "",
      description: "",
      youtube_code: "",
      link_file_video: "",
      id_course: "",
      status: "active",
      ...initialData,
    },
  });

  const youtubeCode = watch("youtube_code");

  React.useEffect(() => {
    if (isOpen && initialData) {
      reset({
        title: initialData.title || "",
        content: initialData.content || "",
        description: initialData.description || "",
        youtube_code: initialData.youtube_code || "",
        id_course: initialData.id_course || "",
        status: initialData.status || "active",
      });

      // Set video type based on initial data
      if (initialData.youtube_code) {
        setVideoType("youtube");
      } else if (initialData.link_file_video) {
        setVideoType("file");
        if (typeof initialData.link_file_video === "string") {
          setVideoPreview(initialData.link_file_video);
        }
      }
    } else if (isOpen && !isEditing) {
      reset({
        title: "",
        content: "",
        description: "",
        youtube_code: "",
        link_file_video: "",
        id_course: "",
        status: "active",
      });
      setVideoFile(null);
      setVideoPreview(null);
      setVideoType(null);
    }
  }, [isOpen, initialData, isEditing, reset]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      processFile(file);
    }
  };

  const processFile = (file: File) => {
    const validationError = validateVideoFile(file);
    if (validationError) {
      alert(validationError);
      return;
    }

    setVideoFile(file);
    setValue("link_file_video", file);
    setVideoType("file");

    // Clear YouTube code when file is selected
    setValue("youtube_code", "");

    // Create preview URL
    const previewUrl = URL.createObjectURL(file);
    setVideoPreview(previewUrl);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      processFile(files[0]);
    }
  };

  const handleYouTubeChange = (url: string) => {
    const videoId = getYouTubeVideoId(url);
    setValue("youtube_code", videoId || "");
    if (url) {
      setVideoType("youtube");
      // Clear file when YouTube URL is entered
      setVideoFile(null);
      setVideoPreview(null);
      setValue("link_file_video", "");
    }
  };

  const removeVideo = () => {
    setVideoFile(null);
    setVideoPreview(null);
    setVideoType(null);
    setValue("link_file_video", "");
    setValue("youtube_code", "");
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const onFormSubmit: SubmitHandler<LessonFormData> = (data) => {
    // Ensure proper handling of video fields
    const formData = { ...data };

    if (formData.youtube_code && formData.youtube_code.trim()) {
      // If YouTube code exists, set link_file_video to empty string
      formData.link_file_video = "";
    } else if (formData.link_file_video) {
      // If file exists, set youtube_code to empty string
      formData.youtube_code = "";
    } else {
      // If neither exists, set both to empty strings
      formData.youtube_code = "";
      formData.link_file_video = "";
    }

    onSubmit(formData);
  };

  const getYouTubeVideoId = (url: string) => {
    return extractYouTubeVideoId(url);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? "Chỉnh sửa Bài học" : "Thêm Bài học Mới"}
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? "Cập nhật thông tin bài học."
              : "Điền thông tin để tạo bài học mới."}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-4">
          {/* Title */}
          <div>
            <Label htmlFor="title">Tiêu đề *</Label>
            <Controller
              name="title"
              control={control}
              render={({ field }) => (
                <Input
                  id="title"
                  placeholder="Nhập tiêu đề bài học"
                  {...field}
                />
              )}
            />
            {errors.title && (
              <p className="text-sm text-red-500 mt-1">{errors.title.message}</p>
            )}
          </div>

          {/* Course */}
          <div>
            <Label htmlFor="course">Học phần *</Label>
            <Controller
              name="id_course"
              control={control}
              render={({ field }) => (
                <Select value={String(field.value)} onValueChange={field.onChange}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Chọn học phần" />
                  </SelectTrigger>
                  <SelectContent>
                    {courseOptions.map((course) => (
                      <SelectItem key={course.id} value={String(course.id)}>
                        {course.name} ({course.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
            {errors.id_course && (
              <p className="text-sm text-red-500 mt-1">{errors.id_course.message}</p>
            )}
          </div>

          {/* Content */}
          <div>
            <Label htmlFor="content">Nội dung *</Label>
            <Controller
              name="content"
              control={control}
              render={({ field }) => (
                <Textarea
                  id="content"
                  placeholder="Nhập nội dung bài học"
                  rows={4}
                  {...field}
                />
              )}
            />
            {errors.content && (
              <p className="text-sm text-red-500 mt-1">{errors.content.message}</p>
            )}
          </div>

          {/* Description */}
          <div>
            <Label htmlFor="description">Mô tả</Label>
            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <Textarea
                  id="description"
                  placeholder="Nhập mô tả bài học (tùy chọn)"
                  rows={3}
                  {...field}
                />
              )}
            />
          </div>

          {/* Video Section */}
          <div className="space-y-4">
            <Label>Video bài học</Label>
            
            {/* YouTube URL Input */}
            <div>
              <Label htmlFor="youtube" className="text-sm">YouTube URL</Label>
              <Controller
                name="youtube_code"
                control={control}
                render={({ field }) => (
                  <div className="flex items-center space-x-2">
                    <Youtube className="h-4 w-4 text-red-500" />
                    <Input
                      id="youtube"
                      placeholder="https://www.youtube.com/watch?v=..."
                      value={`https://www.youtube.com/watch?v=${field.value}` || ""}
                      onChange={(e) => {
                        field.onChange(e.target.value);
                        handleYouTubeChange(e.target.value);
                      }}
                    />
                  </div>
                )}
              />
              {youtubeCode && getYouTubeVideoId(youtubeCode) && (
                <div className="mt-2 p-2 bg-gray-50 rounded">
                  <p className="text-sm text-gray-600">
                    Video ID: {getYouTubeVideoId(youtubeCode)}
                  </p>
                </div>
              )}
            </div>

            <div className="text-center text-sm text-gray-500">hoặc</div>

            {/* File Upload */}
            <div>
              <Label className="text-sm">Tải lên file video</Label>
              <div className="mt-2">
                {!videoFile && !videoPreview ? (
                  <div
                    className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors cursor-pointer ${
                      isDragOver
                        ? "border-blue-400 bg-blue-50"
                        : "border-gray-300 hover:border-gray-400"
                    }`}
                    onClick={() => fileInputRef.current?.click()}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onDrop={handleDrop}
                  >
                    <Upload className={`mx-auto h-12 w-12 ${isDragOver ? "text-blue-500" : "text-gray-400"}`} />
                    <div className="mt-2">
                      <p className={`text-sm ${isDragOver ? "text-blue-700" : "text-gray-600"}`}>
                        {isDragOver ? "Thả file video vào đây" : "Click để tải lên hoặc kéo thả file video"}
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        MP4, AVI, MOV, WMV, FLV (tối đa 100MB)
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="border border-gray-300 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Video className="h-8 w-8 text-blue-500" />
                        <div>
                          <p className="text-sm font-medium">
                            {videoFile ? videoFile.name : "Video file"}
                          </p>
                          {videoFile && (
                            <p className="text-xs text-gray-500">
                              {formatFileSize(videoFile.size)}
                            </p>
                          )}
                        </div>
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={removeVideo}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                )}
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="video/*"
                  className="hidden"
                  onChange={handleFileChange}
                />
              </div>
            </div>
          </div>

          {/* Status */}
          <div>
            <Label htmlFor="status">Trạng thái *</Label>
            <Controller
              name="status"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Chọn trạng thái" />
                  </SelectTrigger>
                  <SelectContent>
                    {LESSON_STATUS_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
            {errors.status && (
              <p className="text-sm text-red-500 mt-1">{errors.status.message}</p>
            )}
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              Hủy
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting
                ? "Đang xử lý..."
                : isEditing
                ? "Cập nhật"
                : "Thêm mới"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
