import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Eye, Edit, Trash2, Video, Link } from "lucide-react";
import { CourseOption, Lesson } from "@/types/lesson";

interface LessonTableProps {
  lessons: Lesson[];
  courseOptions: CourseOption[];
  isLoading: boolean;
  totalRecords: number;
  currentPage: number;
  itemsPerPage: number;
  onView: (lesson: Lesson) => void;
  onEdit: (lesson: Lesson) => void;
  onDelete: (id: string) => void;
}

export default function LessonTable({
  lessons, 
  courseOptions,
  isLoading,
  totalRecords,
  currentPage,
  itemsPerPage,
  onView,
  onEdit,
  onDelete,
}: LessonTableProps) {
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString("vi-VN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
      });
    } catch {
      return "N/A";
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
            Hoạt động
          </Badge>
        );
      case "inactive":
        return (
          <Badge variant="secondary" className="bg-gray-100 text-gray-800 border-gray-200">
            Không hoạt động
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {status}
          </Badge>
        );
    }
  };

  const getVideoTypeIcon = (lesson: Lesson) => {
    if (lesson.youtube_code) {
      return <Video className="h-4 w-4 text-red-500" />;
    }
    if (lesson.link_file_video) {
      return <Link className="h-4 w-4 text-blue-500" />;
    }
    return null;
  };

  const truncateText = (text: string, maxLength: number = 50) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  };

  return (
    <div>
      <Card>
        <CardHeader>
          <CardTitle>Danh sách Bài học</CardTitle>
          <CardDescription>
            Đang có tổng cộng {totalRecords} bài học.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2">Đang tải dữ liệu...</span>
            </div>
          ) : lessons.length === 0 ? (
            <div className="text-center py-8">
              <div className="mx-auto h-12 w-12 text-gray-400 mb-4">
                <Video className="h-full w-full" />
              </div>
              <p className="text-gray-600">Không tìm thấy bài học nào phù hợp.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-blue-50 hover:bg-blue-50">
                    <TableHead className="w-16 text-center">STT</TableHead>
                    <TableHead className="min-w-[200px]">Tiêu đề</TableHead>
                    <TableHead className="min-w-[150px]">Học phần</TableHead>
                    <TableHead className="w-32 text-center">Video</TableHead>
                    <TableHead className="w-32 text-center">Trạng thái</TableHead>
                    
                    <TableHead className="w-48 text-right">Hành động</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {lessons.map((lesson, index) => (
                    <TableRow key={lesson.id} className="hover:bg-gray-50">
                      <TableCell className="text-center font-medium">
                        {(currentPage - 1) * itemsPerPage + index + 1}
                      </TableCell>
                      
                      <TableCell>
                        <div>
                          <div className="font-medium text-gray-900">
                            {truncateText(lesson.title, 40)}
                          </div>
                          {lesson.description && (
                            <div className="text-sm text-gray-500 mt-1">
                              {truncateText(lesson.description, 60)}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        {courseOptions.find(course => course.id === lesson.id_course)?.name || "N/A"}
                      </TableCell>
                      
                      <TableCell className="text-center">
                        <Button variant="outline" size="sm" onClick={() => onView(lesson)}>
                        {getVideoTypeIcon(lesson)}
                        </Button>
                      </TableCell>
                      
                      <TableCell className="text-center">
                        {getStatusBadge(lesson.status)}
                      </TableCell>
                      
                      
                      
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onView(lesson)}
                            className="h-8 w-8 p-0"
                            title="Xem chi tiết"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onEdit(lesson)}
                            className="h-8 w-8 p-0"
                            title="Chỉnh sửa"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => onDelete(lesson.id)}
                            className="h-8 w-8 p-0"
                            title="Xóa"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
