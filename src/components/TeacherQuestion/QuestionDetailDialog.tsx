import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  HelpCircle,
  Calendar,
  CheckCircle,
  Edit,
  Trash2,
  BarChart3,
  Clock,
} from "lucide-react";
import { Question } from "@/types/question";

interface QuestionDetailDialogProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  question: Question | null;
  onEdit?: (question: Question) => void;
  onDelete?: (id: string) => void;
  isLoading?: boolean;
}

export function QuestionDetailDialog({
  isOpen,
  onOpenChange,
  question,
  onEdit,
  onDelete,
  isLoading = false,
}: QuestionDetailDialogProps) {
  if (!question) return null;

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString("vi-VN", {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch {
      return "N/A";
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge className="bg-green-100 text-green-800 border-green-200">
            Hoạt động
          </Badge>
        );
      case "inactive":
        return (
          <Badge variant="secondary" className="bg-gray-100 text-gray-800 border-gray-200">
            Không hoạt động
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getOptionBadge = (option: 'A' | 'B' | 'C' | 'D', isCorrect: boolean) => {
    return (
      <Badge 
        variant={isCorrect ? "default" : "outline"}
        className={isCorrect 
          ? "bg-green-100 text-green-800 border-green-200 font-mono" 
          : "font-mono"
        }
      >
        {option}
        {isCorrect && <CheckCircle className="ml-1 h-3 w-3" />}
      </Badge>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <HelpCircle className="h-5 w-5" />
            Chi tiết Câu hỏi
          </DialogTitle>
          <DialogDescription>
            Thông tin chi tiết về câu hỏi và các đáp án.
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex justify-center items-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Header Info */}
            <div className="flex items-start justify-between">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-500">ID:</span>
                  <Badge variant="outline" className="font-mono">
                    {question.id}
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-500">Trạng thái:</span>
                  {getStatusBadge(question.status)}
                </div>
              </div>
             
            </div>

            <Separator />

            {/* Question Text */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <HelpCircle className="h-5 w-5" />
                Câu hỏi
              </h3>
              <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                <p className="text-gray-900 leading-relaxed">
                  {question.question}
                </p>
              </div>
            </div>

            <Separator />

            {/* Answer Options */}
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">Các đáp án</h3>
              <div className="grid gap-3">
                {/* Option A */}
                <div className="flex items-start gap-3 p-3 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                  <div className="flex-shrink-0">
                    {getOptionBadge('A', question.option_true === 'A')}
                  </div>
                  <div className="flex-1">
                    <p className={`text-gray-900 ${question.option_true === 'A' ? 'font-medium' : ''}`}>
                      {question.option_a}
                    </p>
                  </div>
                </div>

                {/* Option B */}
                <div className="flex items-start gap-3 p-3 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                  <div className="flex-shrink-0">
                    {getOptionBadge('B', question.option_true === 'B')}
                  </div>
                  <div className="flex-1">
                    <p className={`text-gray-900 ${question.option_true === 'B' ? 'font-medium' : ''}`}>
                      {question.option_b}
                    </p>
                  </div>
                </div>

                {/* Option C */}
                <div className="flex items-start gap-3 p-3 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                  <div className="flex-shrink-0">
                    {getOptionBadge('C', question.option_true === 'C')}
                  </div>
                  <div className="flex-1">
                    <p className={`text-gray-900 ${question.option_true === 'C' ? 'font-medium' : ''}`}>
                      {question.option_c}
                    </p>
                  </div>
                </div>

                {/* Option D */}
                <div className="flex items-start gap-3 p-3 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                  <div className="flex-shrink-0">
                    {getOptionBadge('D', question.option_true === 'D')}
                  </div>
                  <div className="flex-1">
                    <p className={`text-gray-900 ${question.option_true === 'D' ? 'font-medium' : ''}`}>
                      {question.option_d}
                    </p>
                  </div>
                </div>
              </div>

              {/* Correct Answer Highlight */}
              <div className="p-3 bg-green-50 rounded-lg border border-green-200">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="font-medium text-green-800">
                    Đáp án đúng: {question.option_true}
                  </span>
                </div>
              </div>
            </div>

            <Separator />

            {/* Question Sets */}
            {question.question_sets && question.question_sets.length > 0 && (
              <>
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold">Thuộc các bộ câu hỏi</h3>
                  <div className="flex flex-wrap gap-2">
                    {question.question_sets.map((set) => (
                      <Badge key={set.id} variant="outline" className="px-3 py-1">
                        {set.name}
                      </Badge>
                    ))}
                  </div>
                </div>
                <Separator />
              </>
            )}

          

            {/* Action Buttons */}
            {(onEdit || onDelete) && (
              <>
                <Separator />
                <div className="flex justify-end gap-2">
                  {onEdit && (
                    <Button
                      variant="outline"
                      onClick={() => onEdit(question)}
                      className="flex items-center gap-2"
                    >
                      <Edit className="h-4 w-4" />
                      Chỉnh sửa
                    </Button>
                  )}
                  {onDelete && (
                    <Button
                      variant="outline"
                      onClick={() => onDelete(question.id)}
                      className="flex items-center gap-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                      Xóa
                    </Button>
                  )}
                </div>
              </>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
