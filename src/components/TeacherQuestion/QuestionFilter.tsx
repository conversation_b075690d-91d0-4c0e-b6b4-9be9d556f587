import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Search, RotateCcw, Filter } from "lucide-react";
import { QuestionFilter, QUESTION_STATUS_OPTIONS } from "@/types/question";

interface QuestionFilterProps {
  filters: QuestionFilter;
  onFiltersChange: (filters: QuestionFilter) => void;
  onSearch: () => void;
  onReset: () => void;
  isLoading?: boolean;
  questionSetOptions?: Array<{ id: number; name: string }>;
  showQuestionSetFilters?: boolean;
}

export function QuestionFilters({
  filters,
  onFiltersChange,
  onSearch,
  onReset,
  isLoading = false,
  questionSetOptions = [],
  showQuestionSetFilters = false,
}: QuestionFilterProps) {
  const handleInputChange = (field: keyof QuestionFilter, value: string) => {
    onFiltersChange({
      ...filters,
      [field]: value,
    });
  };

  const handleSelectChange = (field: keyof QuestionFilter, value: string) => {
    if (field === 'set_id' || field === 'exclude_set_id') {
      onFiltersChange({
        ...filters,
        [field]: value === "all" ? undefined : parseInt(value),
      });
    } else {
      onFiltersChange({
        ...filters,
        [field]: value === "all" ? undefined : value,
      });
    }
  };

  const handleReset = () => {
    onReset();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      onSearch();
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <Filter className="h-5 w-5" />
          Bộ lọc tìm kiếm
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Search Input */}
          <div className="space-y-2">
            <Label htmlFor="search">Tìm kiếm</Label>
            <Input
              id="search"
              placeholder="Tìm theo câu hỏi, đáp án..."
              value={filters.search || ""}
              onChange={(e) => handleInputChange("search", e.target.value)}
              onKeyPress={handleKeyPress}
              className="w-full"
              disabled={isLoading}
            />
          </div>

          {/* Status Filter */}
          <div className="space-y-2">
            <Label htmlFor="status">Trạng thái</Label>
            <Select
              value={filters.status || "all"}
              onValueChange={(value) => handleSelectChange("status", value)}
              disabled={isLoading}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Chọn trạng thái" />
              </SelectTrigger>
              <SelectContent className="w-full">
                <SelectItem value="all">Tất cả trạng thái</SelectItem>
                {QUESTION_STATUS_OPTIONS.map((status) => (
                  <SelectItem key={status.value} value={status.value}>
                    {status.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Question Set Filter - Only show if enabled */}
          {showQuestionSetFilters && (
            <>
              <div className="space-y-2">
                <Label htmlFor="set_id">Thuộc bộ câu hỏi</Label>
                <Select
                  value={filters.set_id?.toString() || "all"}
                  onValueChange={(value) => handleSelectChange("set_id", value)}
                  disabled={isLoading}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Chọn bộ câu hỏi" />
                  </SelectTrigger>
                  <SelectContent className="w-full">
                    <SelectItem value="all">Tất cả bộ câu hỏi</SelectItem>
                    {questionSetOptions.map((set) => (
                      <SelectItem key={set.id} value={set.id.toString()}>
                        {set.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="exclude_set_id">Không thuộc bộ câu hỏi</Label>
                <Select
                  value={filters.exclude_set_id?.toString() || "all"}
                  onValueChange={(value) => handleSelectChange("exclude_set_id", value)}
                  disabled={isLoading}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Loại trừ bộ câu hỏi" />
                  </SelectTrigger>
                  <SelectContent className="w-full">
                    <SelectItem value="all">Không loại trừ</SelectItem>
                    {questionSetOptions.map((set) => (
                      <SelectItem key={set.id} value={set.id.toString()}>
                        {set.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-2 mt-4">
          <Button
            onClick={onSearch}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <Search className="h-4 w-4" />
            {isLoading ? "Đang tìm..." : "Tìm kiếm"}
          </Button>
          <Button
            variant="outline"
            onClick={handleReset}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            Đặt lại
          </Button>
        </div>

        {/* Filter Summary */}
        {(filters.search || filters.status || filters.set_id || filters.exclude_set_id) && (
          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800 font-medium mb-2">Bộ lọc đang áp dụng:</p>
            <div className="flex flex-wrap gap-2">
              {filters.search && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  Tìm kiếm: `{filters.search}``
                </span>
              )}
              {filters.status && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Trạng thái: {QUESTION_STATUS_OPTIONS.find(s => s.value === filters.status)?.label}
                </span>
              )}
              {filters.set_id && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                  Thuộc bộ: {questionSetOptions.find(s => s.id === filters.set_id)?.name}
                </span>
              )}
              {filters.exclude_set_id && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  Loại trừ bộ: {questionSetOptions.find(s => s.id === filters.exclude_set_id)?.name}
                </span>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
