import React from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useForm, Controller, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { HelpCircle, Save, X } from "lucide-react";
import { QuestionFormData, QUESTION_STATUS_OPTIONS, ANSWER_OPTIONS } from "@/types/question";

// Validation schema
const questionSchema = z.object({
  question: z.string().min(1, "Câu hỏi là bắt buộc").max(1000, "Câu hỏi không được quá 1000 ký tự"),
  option_a: z.string().min(1, "Đáp án A là bắt buộc").max(500, "Đáp án A không được quá 500 ký tự"),
  option_b: z.string().min(1, "Đáp án B là bắt buộc").max(500, "Đáp án B không được quá 500 ký tự"),
  option_c: z.string().min(1, "Đáp án C là bắt buộc").max(500, "Đáp án C không được quá 500 ký tự"),
  option_d: z.string().min(1, "Đáp án D là bắt buộc").max(500, "Đáp án D không được quá 500 ký tự"),
  option_true: z.enum(["A", "B", "C", "D"], {
    required_error: "Vui lòng chọn đáp án đúng",
  }),
  status: z.enum(["active", "inactive"]),
});

interface QuestionFormDialogProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  isEditing: boolean;
  onSubmit: SubmitHandler<QuestionFormData>;
  isSubmitting: boolean;
  initialData?: Partial<QuestionFormData>;
}

export function QuestionFormDialog({
  isOpen,
  onOpenChange,
  isEditing,
  onSubmit,
  isSubmitting,
  initialData,
}: QuestionFormDialogProps) {
  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<QuestionFormData>({
    resolver: zodResolver(questionSchema),
    defaultValues: {
      question: "",
      option_a: "",
      option_b: "",
      option_c: "",
      option_d: "",
      option_true: "A",
      status: "active",
      ...initialData,
    },
  });

  const correctAnswer = watch("option_true");

  React.useEffect(() => {
    if (isOpen && initialData) {
      reset({
        question: initialData.question || "",
        option_a: initialData.option_a || "",
        option_b: initialData.option_b || "",
        option_c: initialData.option_c || "",
        option_d: initialData.option_d || "",
        option_true: initialData.option_true || "A",
        status: initialData.status || "active",
      });
    } else if (isOpen && !isEditing) {
      reset({
        question: "",
        option_a: "",
        option_b: "",
        option_c: "",
        option_d: "",
        option_true: "A",
        status: "active",
      });
    }
  }, [isOpen, initialData, isEditing, reset]);

  const handleFormSubmit: SubmitHandler<QuestionFormData> = (data) => {
    onSubmit(data);
  };

  const getOptionStyle = (option: 'A' | 'B' | 'C' | 'D') => {
    return correctAnswer === option 
      ? "border-green-300 bg-green-50 focus:border-green-500 focus:ring-green-500" 
      : "";
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <HelpCircle className="h-5 w-5" />
            {isEditing ? "Chỉnh sửa câu hỏi" : "Thêm câu hỏi mới"}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? "Cập nhật thông tin câu hỏi và các đáp án." 
              : "Tạo câu hỏi mới với 4 đáp án và chọn đáp án đúng."}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          <div className="grid gap-6">
            {/* Question Text */}
            <div className="space-y-2">
              <Label htmlFor="question">
                Câu hỏi <span className="text-red-500">*</span>
              </Label>
              <Controller
                name="question"
                control={control}
                render={({ field }) => (
                  <Textarea
                    {...field}
                    id="question"
                    placeholder="Nhập nội dung câu hỏi..."
                    className="min-h-[100px]"
                    disabled={isSubmitting}
                  />
                )}
              />
              {errors.question && (
                <p className="text-sm text-red-600">{errors.question.message}</p>
              )}
            </div>

            {/* Answer Options */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Option A */}
              <div className="space-y-2">
                <Label htmlFor="option_a" className="flex items-center gap-2">
                  <span className="font-mono bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">A</span>
                  Đáp án A <span className="text-red-500">*</span>
                  {correctAnswer === 'A' && (
                    <span className="text-green-600 text-xs font-medium">(Đáp án đúng)</span>
                  )}
                </Label>
                <Controller
                  name="option_a"
                  control={control}
                  render={({ field }) => (
                    <Textarea
                      {...field}
                      id="option_a"
                      placeholder="Nhập đáp án A..."
                      className={`min-h-[80px] ${getOptionStyle('A')}`}
                      disabled={isSubmitting}
                    />
                  )}
                />
                {errors.option_a && (
                  <p className="text-sm text-red-600">{errors.option_a.message}</p>
                )}
              </div>

              {/* Option B */}
              <div className="space-y-2">
                <Label htmlFor="option_b" className="flex items-center gap-2">
                  <span className="font-mono bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">B</span>
                  Đáp án B <span className="text-red-500">*</span>
                  {correctAnswer === 'B' && (
                    <span className="text-green-600 text-xs font-medium">(Đáp án đúng)</span>
                  )}
                </Label>
                <Controller
                  name="option_b"
                  control={control}
                  render={({ field }) => (
                    <Textarea
                      {...field}
                      id="option_b"
                      placeholder="Nhập đáp án B..."
                      className={`min-h-[80px] ${getOptionStyle('B')}`}
                      disabled={isSubmitting}
                    />
                  )}
                />
                {errors.option_b && (
                  <p className="text-sm text-red-600">{errors.option_b.message}</p>
                )}
              </div>

              {/* Option C */}
              <div className="space-y-2">
                <Label htmlFor="option_c" className="flex items-center gap-2">
                  <span className="font-mono bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">C</span>
                  Đáp án C <span className="text-red-500">*</span>
                  {correctAnswer === 'C' && (
                    <span className="text-green-600 text-xs font-medium">(Đáp án đúng)</span>
                  )}
                </Label>
                <Controller
                  name="option_c"
                  control={control}
                  render={({ field }) => (
                    <Textarea
                      {...field}
                      id="option_c"
                      placeholder="Nhập đáp án C..."
                      className={`min-h-[80px] ${getOptionStyle('C')}`}
                      disabled={isSubmitting}
                    />
                  )}
                />
                {errors.option_c && (
                  <p className="text-sm text-red-600">{errors.option_c.message}</p>
                )}
              </div>

              {/* Option D */}
              <div className="space-y-2">
                <Label htmlFor="option_d" className="flex items-center gap-2">
                  <span className="font-mono bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">D</span>
                  Đáp án D <span className="text-red-500">*</span>
                  {correctAnswer === 'D' && (
                    <span className="text-green-600 text-xs font-medium">(Đáp án đúng)</span>
                  )}
                </Label>
                <Controller
                  name="option_d"
                  control={control}
                  render={({ field }) => (
                    <Textarea
                      {...field}
                      id="option_d"
                      placeholder="Nhập đáp án D..."
                      className={`min-h-[80px] ${getOptionStyle('D')}`}
                      disabled={isSubmitting}
                    />
                  )}
                />
                {errors.option_d && (
                  <p className="text-sm text-red-600">{errors.option_d.message}</p>
                )}
              </div>
            </div>

            {/* Correct Answer and Status */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Correct Answer */}
              <div className="space-y-2">
                <Label htmlFor="option_true">
                  Đáp án đúng <span className="text-red-500">*</span>
                </Label>
                <Controller
                  name="option_true"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                      disabled={isSubmitting}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Chọn đáp án đúng" />
                      </SelectTrigger>
                      <SelectContent>
                        {ANSWER_OPTIONS.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.option_true && (
                  <p className="text-sm text-red-600">{errors.option_true.message}</p>
                )}
              </div>

              {/* Status */}
              <div className="space-y-2">
                <Label htmlFor="status">Trạng thái</Label>
                <Controller
                  name="status"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                      disabled={isSubmitting}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Chọn trạng thái" />
                      </SelectTrigger>
                      <SelectContent>
                        {QUESTION_STATUS_OPTIONS.map((status) => (
                          <SelectItem key={status.value} value={status.value}>
                            {status.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.status && (
                  <p className="text-sm text-red-600">{errors.status.message}</p>
                )}
              </div>
            </div>
          </div>

          <DialogFooter className="gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              <X className="h-4 w-4 mr-2" />
              Hủy
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  {isEditing ? "Đang cập nhật..." : "Đang thêm..."}
                </div>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {isEditing ? "Cập nhật" : "Thêm mới"}
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
