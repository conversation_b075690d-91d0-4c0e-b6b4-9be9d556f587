import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Eye, Edit, Trash2, HelpCircle } from "lucide-react";
import { Question } from "@/types/question";

interface QuestionTableProps {
  questions: Question[];
  isLoading: boolean;
  totalRecords: number;
  currentPage: number;
  itemsPerPage: number;
  onView: (question: Question) => void;
  onEdit: (question: Question) => void;
  onDelete: (id: string) => void;
}

export default function QuestionTable({
  questions,
  isLoading,
  totalRecords,
  currentPage,
  itemsPerPage,
  onView,
  onEdit,
  onDelete,
}: QuestionTableProps) {
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString("vi-VN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
      });
    } catch {
      return "N/A";
    }
  };

  const getStatusBadge = (status: string) => {
    return status === "active" ? (
      <Badge variant="default" className="bg-green-100 text-green-800">
        Hoạt động
      </Badge>
    ) : (
      <Badge variant="secondary" className="bg-red-100 text-red-800">
        Không hoạt động
      </Badge>
    );
  };

const truncateText = (text?: string, maxLength: number = 100) => {
  if (!text) return "";
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + "...";
};


  const getCorrectAnswerText = (question: Question) => {
    switch (question.option_true) {
      case 'A':
        return question.option_a;
      case 'B':
        return question.option_b;
      case 'C':
        return question.option_c;
      case 'D':
        return question.option_d;
      default:
        return 'N/A';
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <HelpCircle className="h-5 w-5" />
            Danh sách câu hỏi
          </CardTitle>
          <CardDescription>
            Đang tải danh sách câu hỏi...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center items-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (questions.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <HelpCircle className="h-5 w-5" />
            Danh sách câu hỏi
          </CardTitle>
          <CardDescription>
            Không tìm thấy câu hỏi nào
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <HelpCircle className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              Không có câu hỏi
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              Chưa có câu hỏi nào được tạo.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const startIndex = (currentPage - 1) * itemsPerPage + 1;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <HelpCircle className="h-5 w-5" />
          Danh sách câu hỏi
        </CardTitle>
        <CardDescription>
          Hiển thị {startIndex} - {Math.min(startIndex + itemsPerPage - 1, totalRecords)} 
          trong tổng số {totalRecords} câu hỏi
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">#</TableHead>
                <TableHead className="min-w-[300px]">Câu hỏi</TableHead>
                <TableHead className="min-w-[200px]">Đáp án đúng</TableHead>
                <TableHead className="w-32">Trạng thái</TableHead>
                <TableHead className="w-32">Thao tác</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {questions.map((question, index) => (
                <TableRow key={question.id}>
                  <TableCell className="font-medium">
                    {startIndex + index}
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <p className="font-medium">
                        Câu hỏi: {truncateText(question.question, 80)}
                      </p>
                      <div className="text-xs text-gray-500 space-y-1">
                        <div>A. {truncateText(question.option_a, 40)}</div>
                        <div>B. {truncateText(question.option_b, 40)}</div>
                        <div>C. {truncateText(question.option_c, 40)}</div>
                        <div>D. {truncateText(question.option_d, 40)}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="font-mono">
                        {question.option_true}
                      </Badge>
                      <p className="text-sm text-gray-600">
                        {truncateText(getCorrectAnswerText(question), 60)}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(question.status)}</TableCell>
                  
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onView(question)}
                        className="h-8 w-8 p-0"
                        title="Xem chi tiết"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onEdit(question)}
                        className="h-8 w-8 p-0"
                        title="Chỉnh sửa"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onDelete(question.id)}
                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                        title="Xóa"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
