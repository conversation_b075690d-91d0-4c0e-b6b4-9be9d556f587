# Question Management System

A comprehensive question management system for teachers with full CRUD operations, search, filtering, and pagination capabilities.

## Features

### 1. Question List Management
- **API Integration**: Uses `https://devqlth.phoenixtech.vn/apis/listQuestionsAPI`
- **Pagination**: 20 items per page with full pagination controls
- **Search**: Search by question text and answer options
- **Filtering**: Filter by status (active/inactive)
- **Two distinct use cases**:
  - Display questions that belong to a specific question set (using `set_id`)
  - Display questions that are NOT in a specific question set (using `exclude_set_id`)

### 2. Add/Edit Question Functionality
- **Form Validation**: Comprehensive validation using Zod schema
- **Question Text**: Required, max 1000 characters
- **Answer Options**: 4 options (A, B, C, D), each required, max 500 characters
- **Correct Answer**: Select from A, B, C, D
- **Status**: Active or Inactive
- **Visual Feedback**: Correct answer option is highlighted in the form

### 3. Delete Question Functionality
- **API Integration**: Uses `https://devqlth.phoenixtech.vn/apis/deleteQuestionAPI`
- **Confirmation Dialog**: Prevents accidental deletions
- **Force Delete**: Supports force delete even if question belongs to a question set

### 4. Question Detail View
- **Read-only Dialog**: View complete question details
- **Answer Highlighting**: Correct answer is visually highlighted
- **Usage Statistics**: Shows usage count and timestamps
- **Question Sets**: Shows which question sets the question belongs to

## Components

### QuestionTable
- Displays questions in a responsive table format
- Shows question text (truncated), all 4 options (truncated), correct answer, status, usage count
- Action buttons: View, Edit, Delete
- Loading states and empty states

### QuestionFormDialog
- Modal dialog for adding/editing questions
- Form validation with error messages
- Visual feedback for correct answer selection
- Responsive design (mobile-friendly)

### QuestionFilter
- Search by question text and answers
- Filter by status
- Optional question set filters (can be enabled/disabled)
- Filter summary display
- Reset functionality

### QuestionDetailDialog
- Read-only view of complete question details
- All answer options with correct answer highlighted
- Metadata: creation date, update date, usage count
- Quick action buttons for edit/delete

## API Integration

### List Questions
```typescript
const response = await listQuestionsAPI(filters, page, token, limit);
```

**Request Parameters:**
- `token`: Authentication token
- `set_id`: Filter by question set ID (optional)
- `exclude_set_id`: Get questions NOT in this question set (optional)
- `search`: Search by question text or answer options (optional)
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)

### Add/Edit Question
```typescript
const response = await addQuestionAPI(data, token, isEdit, questionId);
```

**Request Data:**
- `question_text`: Question content
- `option_a`, `option_b`, `option_c`, `option_d`: Answer options
- `correct_answer`: 'A' | 'B' | 'C' | 'D'
- `status`: 'active' | 'inactive'

### Delete Question
```typescript
const response = await deleteQuestionAPI(token, id, forceDelete);
```

**Request Parameters:**
- `token`: Authentication token
- `id`: Question ID to delete
- `force_delete`: Force delete even if question belongs to a question set

## Usage

### Basic Usage
```tsx
import TeacherQuestionPage from '@/app/(teacher)/teacherQuestion/page';

// The page component handles all state management internally
<TeacherQuestionPage />
```

### Individual Components
```tsx
import QuestionTable from '@/components/TeacherQuestion/QuestionTable';
import { QuestionFormDialog } from '@/components/TeacherQuestion/QuestionFormDialog';
import { QuestionFilters } from '@/components/TeacherQuestion/QuestionFilter';

// Use individual components for custom implementations
```

## State Management

The system uses local React state with the following structure:

- **TableState**: Questions list, pagination, loading states
- **FormState**: Form dialog state, edit mode, submission state
- **DetailState**: Detail dialog state, selected question
- **DeleteState**: Delete confirmation dialog state

## Error Handling

- API errors are displayed using toast notifications
- Form validation errors are shown inline
- Loading states prevent multiple submissions
- Network errors are gracefully handled

## Responsive Design

- Mobile-friendly table with horizontal scrolling
- Responsive form dialogs
- Adaptive grid layouts for different screen sizes
- Touch-friendly action buttons

## Accessibility

- Proper ARIA labels and roles
- Keyboard navigation support
- Screen reader friendly
- High contrast support
- Focus management in dialogs

## Performance

- Efficient pagination (20 items per page)
- Debounced search functionality
- Optimized re-renders with useCallback
- Lazy loading of question details
- Minimal API calls with proper caching
