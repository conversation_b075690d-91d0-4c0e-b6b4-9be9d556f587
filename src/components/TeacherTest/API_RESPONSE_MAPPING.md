# Test Management API Response Mapping

## API Response Structure Handling

This document outlines how the test management system handles the API response structure from `detailTestAPI`.

## Original API Response Format

```json
{
    "data": {
        "test": {
            "id": 2,
            "description": "<PERSON><PERSON><PERSON> tra chương 1 đến chương 3",
            "time_test": 45,
            "title": "<PERSON><PERSON>i kiểm tra giữa kỳ",
            "id_lesson": 3,
            "id_question": "[\"1\",\"3\",\"4\",\"5\"]",
            "status": "active",
            "time_start": 1754554860,
            "time_end": 1754565660,
            "point_min": 5
        },
        "questions": [
            {
                "id": 1,
                "question": "Câu hỏi số 1?",
                "option_a": "Đáp án A1",
                "option_b": "Đáp án B1",
                "option_c": "Đáp án C1",
                "option_d": "Đáp án D1",
                "option_true": "A",
                "status": "active"
            }
        ],
        "total_questions": 4
    },
    "code": 0,
    "mess": "Lấy thông tin bài test thành công"
}
```

## Data Transformation Process

### 1. API Response Parsing (`detailTestAPI`)

The `detailTestAPI` function in `src/api/teacher/testAPI.ts` handles the response transformation:

```typescript
// Extract test data and questions from nested structure
const testData = response.data.data.test;
const questionsData = response.data.data.questions || [];

// Convert Unix timestamps to datetime strings
const formatUnixToDateTime = (timestamp: number): string => {
  const date = new Date(timestamp * 1000);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// Map questions to TestQuestion format
const mappedQuestions = questionsData.map((q) => ({
  question: q.question,
  option_a: q.option_a,
  option_b: q.option_b,
  option_c: q.option_c,
  option_d: q.option_d,
  option_true: q.option_true as 'A' | 'B' | 'C' | 'D',
}));

// Return mapped test data
return {
  id: testData.id.toString(),
  title: testData.title,
  description: testData.description || "",
  id_lesson: testData.id_lesson.toString(),
  time_test: testData.time_test,
  time_start: formatUnixToDateTime(testData.time_start),
  time_end: formatUnixToDateTime(testData.time_end),
  status: testData.status as 'active' | 'inactive',
  point_min: testData.point_min,
  questions: mappedQuestions,
};
```

### 2. Key Transformations

#### **Timestamp Conversion**
- **Input**: Unix timestamp (e.g., `1754554860`)
- **Output**: Datetime string (e.g., `"2025-08-06 14:21:00"`)
- **Purpose**: Convert API timestamps to readable format

#### **ID Conversion**
- **Input**: Numeric IDs (e.g., `2`, `3`)
- **Output**: String IDs (e.g., `"2"`, `"3"`)
- **Purpose**: Ensure consistency with form expectations

#### **Questions Array Mapping**
- **Input**: API question objects with `id` and `status` fields
- **Output**: TestQuestion objects with only required fields
- **Purpose**: Remove unnecessary fields and ensure type safety

#### **Optional Fields Handling**
- **Description**: Defaults to empty string if not provided
- **Questions**: Defaults to empty array if not provided

### 3. Form Integration

#### **DateTime Input Handling**
The form uses `datetime-local` inputs which require a specific format:

```typescript
// Convert API datetime string to form input format
export const formatDateTimeForForm = (dateTimeString: string): string => {
  try {
    const date = parseDateTimeFromAPI(dateTimeString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  } catch {
    return "";
  }
};
```

#### **Form Field Mapping**
- **time_start**: Unix timestamp → datetime string → datetime-local format
- **time_end**: Unix timestamp → datetime string → datetime-local format
- **questions**: API questions array → TestQuestion array → form fields

### 4. Data Flow

```
API Response (Unix timestamps, nested structure)
    ↓
detailTestAPI transformation
    ↓
Test object (datetime strings, flat structure)
    ↓
Form initialization (datetime-local format)
    ↓
User interaction
    ↓
Form submission (API format)
```

### 5. Error Handling

#### **API Response Validation**
- Check for `code === 0` (success code)
- Validate presence of `data.test` and `data.questions`
- Provide meaningful error messages

#### **Data Transformation Safety**
- Handle missing optional fields
- Type casting with proper validation
- Fallback values for invalid data

#### **Form Integration Safety**
- Try-catch blocks for datetime conversion
- Empty string fallbacks for invalid dates
- Proper error messaging to users

### 6. Testing Scenarios

#### **Valid API Response**
- ✅ Test with complete data including questions
- ✅ Test with missing optional fields (description)
- ✅ Test with empty questions array
- ✅ Test timestamp conversion accuracy

#### **Invalid API Response**
- ✅ Test with missing test data
- ✅ Test with invalid timestamps
- ✅ Test with malformed question data
- ✅ Test error handling and user feedback

#### **Form Integration**
- ✅ Test form population with API data
- ✅ Test datetime input display
- ✅ Test question array handling
- ✅ Test form submission with converted data

### 7. Benefits of This Approach

#### **Data Integrity**
- Consistent data types throughout the application
- Proper validation and error handling
- Safe fallbacks for missing data

#### **User Experience**
- Proper datetime display in forms
- Complete question data in edit mode
- Meaningful error messages

#### **Maintainability**
- Clear separation of concerns
- Reusable transformation functions
- Well-documented data flow

#### **Type Safety**
- TypeScript interfaces ensure correct data structure
- Compile-time validation of data transformations
- Runtime type checking where needed
