import React, { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search, Plus, Trash2, HelpCircle, CheckCircle } from "lucide-react";
import { Question } from "@/types/question";
import { TestQuestion } from "@/types/test";
import { listQuestionsAPI } from "@/api/teacher/questionAPI";
import { getAuthToken } from "@/utils/getAuthToken";
import { toast } from "sonner";

interface QuestionSelectorProps {
  selectedQuestions: TestQuestion[];
  onQuestionsChange: (questions: TestQuestion[]) => void;
  disabled?: boolean;
}

export function QuestionSelector({
  selectedQuestions,
  onQuestionsChange,
  disabled = false,
}: QuestionSelectorProps) {
  const [availableQuestions, setAvailableQuestions] = useState<Question[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<"all" | "active" | "inactive">("active");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Load available questions
  const loadQuestions = useCallback(async (page: number = 1, search?: string, status?: string) => {
    const token = getAuthToken();
    if (!token) {
      toast.error("Không tìm thấy token xác thực");
      return;
    }

    setIsLoading(true);
    try {
      const filters: any = {};
      if (search) filters.search = search;
      if (status && status !== "all") filters.status = status;

      const response = await listQuestionsAPI(filters, page, token, 10);
      setAvailableQuestions(response.data);
      setTotalPages(response.totalPage || Math.ceil(response.totalData / 10));
      setCurrentPage(page);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi tải danh sách câu hỏi";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Initial load
  useEffect(() => {
    loadQuestions();
  }, [loadQuestions]);

  // Handle search
  const handleSearch = () => {
    loadQuestions(1, searchTerm, statusFilter);
  };

  // Handle filter change
  const handleFilterChange = (status: string) => {
    setStatusFilter(status as "all" | "active" | "inactive");
    loadQuestions(1, searchTerm, status);
  };

  // Convert Question to TestQuestion
  const convertToTestQuestion = (question: Question): TestQuestion => ({
    question: question.question,
    option_a: question.option_a,
    option_b: question.option_b,
    option_c: question.option_c,
    option_d: question.option_d,
    option_true: question.option_true,
  });

  // Check if question is already selected
  const isQuestionSelected = (question: Question): boolean => {
    return selectedQuestions.some(selected => 
      selected.question === question.question &&
      selected.option_a === question.option_a &&
      selected.option_b === question.option_b &&
      selected.option_c === question.option_c &&
      selected.option_d === question.option_d
    );
  };

  // Handle question selection
  const handleQuestionToggle = (question: Question, isSelected: boolean) => {
    if (isSelected) {
      // Add question
      const testQuestion = convertToTestQuestion(question);
      onQuestionsChange([...selectedQuestions, testQuestion]);
    } else {
      // Remove question
      const updatedQuestions = selectedQuestions.filter(selected => 
        !(selected.question === question.question &&
          selected.option_a === question.option_a &&
          selected.option_b === question.option_b &&
          selected.option_c === question.option_c &&
          selected.option_d === question.option_d)
      );
      onQuestionsChange(updatedQuestions);
    }
  };

  // Remove selected question
  const handleRemoveQuestion = (index: number) => {
    const updatedQuestions = selectedQuestions.filter((_, i) => i !== index);
    onQuestionsChange(updatedQuestions);
  };

  const getCorrectAnswerText = (question: Question) => {
    switch (question.option_true) {
      case 'A': return question.option_a;
      case 'B': return question.option_b;
      case 'C': return question.option_c;
      case 'D': return question.option_d;
      default: return 'N/A';
    }
  };

  const truncateText = (text: string, maxLength: number = 100) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  };

  return (
    <div className="space-y-6">
      {/* Question Browser */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <HelpCircle className="h-5 w-5" />
            Chọn câu hỏi từ ngân hàng câu hỏi
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search and Filter */}
          <div className="flex gap-4">
            <div className="flex-1">
              <Label htmlFor="search">Tìm kiếm câu hỏi</Label>
              <div className="flex gap-2">
                <Input
                  id="search"
                  placeholder="Nhập từ khóa tìm kiếm..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && handleSearch()}
                  disabled={disabled || isLoading}
                />
                <Button onClick={handleSearch} disabled={disabled || isLoading}>
                  <Search className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="w-48">
              <Label htmlFor="status">Trạng thái</Label>
              <Select
                value={statusFilter}
                onValueChange={handleFilterChange}
                disabled={disabled || isLoading}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả</SelectItem>
                  <SelectItem value="active">Hoạt động</SelectItem>
                  <SelectItem value="inactive">Không hoạt động</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Question List */}
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {isLoading ? (
              <div className="flex justify-center items-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : availableQuestions.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                Không tìm thấy câu hỏi nào
              </div>
            ) : (
              availableQuestions.map((question) => {
                const isSelected = isQuestionSelected(question);
                return (
                  <div
                    key={question.id}
                    className={`p-4 border rounded-lg ${
                      isSelected ? "border-green-300 bg-green-50" : "border-gray-200"
                    }`}
                  >
                    <div className="flex items-start gap-3">
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={(checked) => 
                          handleQuestionToggle(question, checked as boolean)
                        }
                        disabled={disabled}
                      />
                      <div className="flex-1 space-y-2">
                        <p className="font-medium">{truncateText(question.question)}</p>
                        <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
                          <div>A. {truncateText(question.option_a, 50)}</div>
                          <div>B. {truncateText(question.option_b, 50)}</div>
                          <div>C. {truncateText(question.option_c, 50)}</div>
                          <div>D. {truncateText(question.option_d, 50)}</div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="font-mono">
                            Đáp án: {question.option_true}
                          </Badge>
                          <span className="text-sm text-gray-500">
                            {truncateText(getCorrectAnswerText(question), 40)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })
            )}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => loadQuestions(currentPage - 1, searchTerm, statusFilter)}
                disabled={currentPage <= 1 || isLoading || disabled}
              >
                Trước
              </Button>
              <span className="flex items-center px-3 text-sm">
                Trang {currentPage} / {totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => loadQuestions(currentPage + 1, searchTerm, statusFilter)}
                disabled={currentPage >= totalPages || isLoading || disabled}
              >
                Sau
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Selected Questions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            Câu hỏi đã chọn ({selectedQuestions.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {selectedQuestions.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              Chưa chọn câu hỏi nào
            </div>
          ) : (
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {selectedQuestions.map((question, index) => (
                <div key={index} className="p-3 border border-green-200 bg-green-50 rounded-lg">
                  <div className="flex items-start justify-between gap-3">
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="font-mono">
                          {index + 1}
                        </Badge>
                        <p className="font-medium">{truncateText(question.question)}</p>
                      </div>
                      <div className="text-sm text-gray-600">
                        <span className="font-medium">Đáp án đúng: {question.option_true}</span>
                        {" - "}
                        <span>
                          {question.option_true === 'A' && question.option_a}
                          {question.option_true === 'B' && question.option_b}
                          {question.option_true === 'C' && question.option_c}
                          {question.option_true === 'D' && question.option_d}
                        </span>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveQuestion(index)}
                      disabled={disabled}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
