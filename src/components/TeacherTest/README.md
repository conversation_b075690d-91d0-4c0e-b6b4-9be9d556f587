# Test Management System

A comprehensive test management system for teachers with full CRUD operations, search, filtering, pagination, and dynamic question management capabilities.

## Features

### 1. Test List Management
- **API Integration**: Uses `https://devqlth.phoenixtech.vn/apis/listTestsAPI`
- **Pagination**: 20 items per page with full pagination controls
- **Search**: Search by test title and description
- **Filtering**: Filter by status (active/inactive) and lesson ID
- **Real-time Status**: Shows test status (upcoming, ongoing, ended) based on current time

### 2. Add/Edit Test Functionality
- **API Integration**: Uses `https://devqlth.phoenixtech.vn/apis/addTestAPI`
- **Comprehensive Form**: Test title, description, lesson selection, duration, start/end times, status, minimum score
- **Dynamic Question Management**: Add/remove questions with multiple choice options
- **Date/Time Validation**: Prevents invalid date ranges and past dates
- **Form Validation**: Comprehensive validation using Zod schema

### 3. Test Detail View
- **API Integration**: Uses `https://devqlth.phoenixtech.vn/apis/detailTestAPI`
- **Complete Information**: Shows all test metadata and configuration
- **Question Display**: Shows all questions with correct answers highlighted
- **Status Indicators**: Visual indicators for test timing and status

### 4. Delete Test Functionality
- **API Integration**: Uses `https://devqlth.phoenixtech.vn/apis/deleteTestAPI`
- **Confirmation Dialog**: Prevents accidental deletions
- **Success/Error Handling**: Proper feedback for all operations

## Components

### TestTable
- Displays tests in a responsive table format
- Shows title, lesson, duration, start/end times, status, minimum score
- Real-time status indicators (upcoming, ongoing, ended)
- Action buttons: View, Edit, Delete
- Loading states and empty states

### TestFormDialog
- Modal dialog for adding/editing tests
- Lesson selection dropdown with course information
- Date/time pickers with validation
- Dynamic question management (add/remove questions)
- Real-time form validation with error messages
- Visual feedback for correct answer selection

### TestFilter
- Search by test title and description
- Filter by status and lesson
- Quick filter buttons for common scenarios
- Filter summary display
- Reset functionality

### TestDetailDialog
- Read-only view of complete test details
- Test configuration and schedule information
- All questions with correct answers highlighted
- Quick action buttons for edit/delete

## API Integration

### List Tests
```typescript
const response = await listTestsAPI(filters, page, token, limit);
```

**Request Parameters:**
- `token`: Authentication token
- `limit`: Items per page (default: 10)
- `page`: Page number (default: 1)
- `search`: Search by title or description (optional)
- `status`: Filter by status (optional)
- `id_lesson`: Filter by lesson ID (optional)

### Add/Edit Test
```typescript
const response = await addTestAPI(data, token, isEdit, testId);
```

**Request Data:**
- `title`: Test title
- `description`: Test description (optional)
- `id_lesson`: Lesson ID
- `time_test`: Duration in minutes
- `time_start`: Start datetime (YYYY-MM-DD HH:MM:SS)
- `time_end`: End datetime (YYYY-MM-DD HH:MM:SS)
- `status`: 'active' | 'inactive'
- `point_min`: Minimum passing score
- `questions`: Array of questions with options and correct answers

### Test Detail
```typescript
const response = await detailTestAPI(token, id);
```

### Delete Test
```typescript
const response = await deleteTestAPI(token, id);
```

## Usage

### Basic Usage
```tsx
import TeacherTestPage from '@/app/(teacher)/teacherTest/page';

// The page component handles all state management internally
<TeacherTestPage />
```

### Individual Components
```tsx
import TestTable from '@/components/TeacherTest/TestTable';
import { TestFormDialog } from '@/components/TeacherTest/TestFormDialog';
import { TestFilters } from '@/components/TeacherTest/TestFilter';

// Use individual components for custom implementations
```

## State Management

The system uses local React state with the following structure:

- **TestTableState**: Tests list, pagination, loading states, filters
- **TestFormState**: Form dialog state, edit mode, submission state
- **TestDetailState**: Detail dialog state, selected test
- **DeleteState**: Delete confirmation dialog state

## Form Validation

### Test Configuration Validation
- **Title**: Required, max 255 characters
- **Duration**: 5-300 minutes
- **Dates**: Start time must be in future, end time must be after start time
- **Minimum Score**: 0-10 points
- **Questions**: 1-100 questions required

### Question Validation
- **Question Text**: Required, max 1000 characters
- **Options A-D**: All required, max 500 characters each
- **Correct Answer**: Must select one of A, B, C, D

## Date/Time Handling

- **Input Format**: HTML5 datetime-local input
- **API Format**: YYYY-MM-DD HH:MM:SS
- **Validation**: Prevents past dates and invalid ranges
- **Display Format**: Localized Vietnamese format

## Error Handling

- API errors are displayed using toast notifications
- Form validation errors are shown inline
- Loading states prevent multiple submissions
- Network errors are gracefully handled
- Date/time validation with user-friendly messages

## Responsive Design

- Mobile-friendly table with horizontal scrolling
- Responsive form dialogs with adaptive layouts
- Touch-friendly action buttons
- Collapsible sections for mobile

## Accessibility

- Proper ARIA labels and roles
- Keyboard navigation support
- Screen reader friendly
- High contrast support
- Focus management in dialogs

## Performance

- Efficient pagination (20 items per page)
- Optimized re-renders with useCallback
- Lazy loading of lesson options
- Minimal API calls with proper caching
- Dynamic question management without full re-renders

## Integration with Question Management

- Reuses question types and validation from question management system
- Consistent UI patterns for question handling
- Shared answer option constants and validation

## Configuration

### Test Limits
- Minimum duration: 5 minutes
- Maximum duration: 300 minutes (5 hours)
- Minimum questions: 1
- Maximum questions: 100
- Minimum passing score: 0
- Maximum passing score: 10

### Pagination
- Default page size: 20 items
- Available page sizes: 10, 20, 50, 100

## Security

- Token-based authentication for all API calls
- Input validation and sanitization
- XSS protection through proper escaping
- CSRF protection through token validation
