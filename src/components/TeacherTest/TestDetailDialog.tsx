import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  FileText,
  Calendar,
  CheckCircle,
  Edit,
  Trash2,
  Clock,
  BookOpen,
  Target,
  HelpCircle,
} from "lucide-react";
import { Test, LessonOption } from "@/types/test";
import { formatDuration } from "@/api/teacher/testAPI";

interface TestDetailDialogProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  test: Test | null;
  lessonOptions: LessonOption[];
  onEdit?: (test: Test) => void;
  onDelete?: (id: string) => void;
  isLoading?: boolean;
}

export function TestDetailDialog({
  isOpen,
  onOpenChange,
  test,
  lessonOptions,
  onEdit,
  onDelete,
  isLoading = false,
}: TestDetailDialogProps) {
  if (!test) return null;


   const getLessonInfo = (id_lesson: string) => {
    return lessonOptions.find(lesson => lesson.id == id_lesson);
  };

  const formatDateTime = (dateTimeString: string) => {
    try {
      return new Date(dateTimeString).toLocaleDateString("vi-VN", {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch {
      return "N/A";
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge className="bg-green-100 text-green-800 border-green-200">
            Hoạt động
          </Badge>
        );
      case "inactive":
        return (
          <Badge variant="secondary" className="bg-gray-100 text-gray-800 border-gray-200">
            Không hoạt động
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getTestStatus = (test: Test) => {
    const now = new Date();
    const startTime = new Date(test.time_start);
    const endTime = new Date(test.time_end);

    if (now < startTime) {
      return { status: 'upcoming', label: 'Sắp diễn ra', color: 'bg-blue-100 text-blue-800' };
    } else if (now >= startTime && now <= endTime) {
      return { status: 'ongoing', label: 'Đang diễn ra', color: 'bg-yellow-100 text-yellow-800' };
    } else {
      return { status: 'ended', label: 'Đã kết thúc', color: 'bg-gray-100 text-gray-800' };
    }
  };

  const getOptionBadge = (option: 'A' | 'B' | 'C' | 'D', isCorrect: boolean) => {
    return (
      <Badge 
        variant={isCorrect ? "default" : "outline"}
        className={isCorrect 
          ? "bg-green-100 text-green-800 border-green-200 font-mono" 
          : "font-mono"
        }
      >
        {option}
        {isCorrect && <CheckCircle className="ml-1 h-3 w-3" />}
      </Badge>
    );
  };


  const testStatus = getTestStatus(test);

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Chi tiết Bài kiểm tra
          </DialogTitle>
          <DialogDescription>
            Thông tin chi tiết về bài kiểm tra và các câu hỏi.
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex justify-center items-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Header Info */}
            <div className="flex items-start justify-between">
              <div className="space-y-2">
                <h2 className="text-xl font-semibold">{test.title}</h2>
                {test.description && (
                  <p className="text-gray-600">{test.description}</p>
                )}
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-500">Trạng thái:</span>
                  {getStatusBadge(test.status)}
                  <Badge variant="outline" className={testStatus.color}>
                    {testStatus.label}
                  </Badge>
                </div>
              </div>
            </div>

            <Separator />

            {/* Test Configuration */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <BookOpen className="h-4 w-4" />
                  <span>Bài học</span>
                </div>
                <div>
                  {(() => {
                    const lessonInfo = getLessonInfo(test.id_lesson);
                  
                    return (
                      <>
                        {lessonInfo?.title}
                      </>
                    );
                  })()}
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <Clock className="h-4 w-4" />
                  <span>Thời gian làm bài</span>
                </div>
                <p className="font-medium">{formatDuration(test.time_test)}</p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <Target className="h-4 w-4" />
                  <span>Điểm tối thiểu</span>
                </div>
                <p className="font-medium">{test.point_min}/10</p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <HelpCircle className="h-4 w-4" />
                  <span>Số câu hỏi</span>
                </div>
                <p className="font-medium">{test.questions?.length || 0} câu</p>
              </div>
            </div>

            <Separator />

            {/* Schedule */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <Calendar className="h-4 w-4" />
                  <span>Thời gian bắt đầu</span>
                </div>
                <p className="font-medium">{formatDateTime(test.time_start)}</p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <Calendar className="h-4 w-4" />
                  <span>Thời gian kết thúc</span>
                </div>
                <p className="font-medium">{formatDateTime(test.time_end)}</p>
              </div>
            </div>

            <Separator />

            {/* Questions */}
            {test.questions && test.questions.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <HelpCircle className="h-5 w-5" />
                  Câu hỏi ({test.questions.length})
                </h3>
                
                <div className="space-y-6">
                  {test.questions.map((question, index) => (
                    <div key={index} className="p-4 border border-gray-200 rounded-lg space-y-4">
                      <div className="flex items-start gap-3">
                        <Badge variant="outline" className="font-mono mt-1">
                          {index + 1}
                        </Badge>
                        <div className="flex-1">
                          <p className="font-medium text-gray-900 leading-relaxed">
                            {question.question}
                          </p>
                        </div>
                      </div>

                      <div className="grid gap-3 ml-8">
                        {/* Option A */}
                        <div className="flex items-start gap-3 p-2 rounded border border-gray-100">
                          <div className="flex-shrink-0">
                            {getOptionBadge('A', question.option_true === 'A')}
                          </div>
                          <div className="flex-1">
                            <p className={`text-gray-900 ${question.option_true === 'A' ? 'font-medium' : ''}`}>
                              {question.option_a}
                            </p>
                          </div>
                        </div>

                        {/* Option B */}
                        <div className="flex items-start gap-3 p-2 rounded border border-gray-100">
                          <div className="flex-shrink-0">
                            {getOptionBadge('B', question.option_true === 'B')}
                          </div>
                          <div className="flex-1">
                            <p className={`text-gray-900 ${question.option_true === 'B' ? 'font-medium' : ''}`}>
                              {question.option_b}
                            </p>
                          </div>
                        </div>

                        {/* Option C */}
                        <div className="flex items-start gap-3 p-2 rounded border border-gray-100">
                          <div className="flex-shrink-0">
                            {getOptionBadge('C', question.option_true === 'C')}
                          </div>
                          <div className="flex-1">
                            <p className={`text-gray-900 ${question.option_true === 'C' ? 'font-medium' : ''}`}>
                              {question.option_c}
                            </p>
                          </div>
                        </div>

                        {/* Option D */}
                        <div className="flex items-start gap-3 p-2 rounded border border-gray-100">
                          <div className="flex-shrink-0">
                            {getOptionBadge('D', question.option_true === 'D')}
                          </div>
                          <div className="flex-1">
                            <p className={`text-gray-900 ${question.option_true === 'D' ? 'font-medium' : ''}`}>
                              {question.option_d}
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* Correct Answer Highlight */}
                      <div className="ml-8 p-2 bg-green-50 rounded border border-green-200">
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <span className="text-sm font-medium text-green-800">
                            Đáp án đúng: {question.option_true}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            {(onEdit || onDelete) && (
              <>
                <Separator />
                <div className="flex justify-end gap-2">
                  {onEdit && (
                    <Button
                      variant="outline"
                      onClick={() => onEdit(test)}
                      className="flex items-center gap-2"
                    >
                      <Edit className="h-4 w-4" />
                      Chỉnh sửa
                    </Button>
                  )}
                  {onDelete && (
                    <Button
                      variant="outline"
                      onClick={() => onDelete(test.id)}
                      className="flex items-center gap-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                      Xóa
                    </Button>
                  )}
                </div>
              </>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
