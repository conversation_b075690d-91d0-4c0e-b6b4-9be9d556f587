import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Search, RotateCcw, Filter } from "lucide-react";
import { TestFilter, TEST_STATUS_OPTIONS, LessonOption } from "@/types/test";

interface TestFilterProps {
  filters: TestFilter;
  onFiltersChange: (filters: TestFilter) => void;
  onSearch: () => void;
  onReset: () => void;
  isLoading?: boolean;
  lessonOptions?: LessonOption[];
}

export function TestFilters({
  filters,
  onFiltersChange,
  onSearch,
  onReset,
  isLoading = false,
  lessonOptions = [],
}: TestFilterProps) {
  const handleInputChange = (field: keyof TestFilter, value: string) => {
    onFiltersChange({
      ...filters,
      [field]: value,
    });
  };

  const handleSelectChange = (field: keyof TestFilter, value: string) => {
    onFiltersChange({
      ...filters,
      [field]: value === "all" ? undefined : value,
    });
  };

  const handleReset = () => {
    onReset();
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      onSearch();
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <Filter className="h-5 w-5" />
          Bộ lọc tìm kiếm
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* Search Input */}
          <div className="space-y-2">
            <Label htmlFor="search">Tìm kiếm</Label>
            <Input
              id="search"
              placeholder="Tìm theo tiêu đề, mô tả..."
              value={filters.search || ""}
              onChange={(e) => handleInputChange("search", e.target.value)}
              onKeyDown={handleKeyDown}
              className="w-full"
              disabled={isLoading}
            />
          </div>

          {/* Status Filter */}
          <div className="space-y-2">
            <Label htmlFor="status">Trạng thái</Label>
            <Select
              value={filters.status || "all"}
              onValueChange={(value) => handleSelectChange("status", value)}
              disabled={isLoading}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Chọn trạng thái" />
              </SelectTrigger>
              <SelectContent className="w-full">
                <SelectItem value="all">Tất cả trạng thái</SelectItem>
                {TEST_STATUS_OPTIONS.map((status) => (
                  <SelectItem key={status.value} value={status.value}>
                    {status.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Lesson Filter */}
          <div className="space-y-2">
            <Label htmlFor="id_lesson">Bài học</Label>
            <Select
              value={filters.id_lesson || "all"}
              onValueChange={(value) => handleSelectChange("id_lesson", value)}
              disabled={isLoading}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Chọn bài học" />
              </SelectTrigger>
              <SelectContent className="w-full max-h-60">
                <SelectItem value="all">Tất cả bài học</SelectItem>
                {lessonOptions.map((lesson) => (
                  <SelectItem key={lesson.id} value={lesson.id}>
                    {lesson.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-2 mt-4">
          <Button
            onClick={onSearch}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <Search className="h-4 w-4" />
            {isLoading ? "Đang tìm..." : "Tìm kiếm"}
          </Button>
          <Button
            variant="outline"
            onClick={handleReset}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            Đặt lại
          </Button>
        </div>

        {/* Filter Summary */}
        {(filters.search || filters.status || filters.id_lesson) && (
          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800 font-medium mb-2">Bộ lọc đang áp dụng:</p>
            <div className="flex flex-wrap gap-2">
              {filters.search && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  Tìm kiếm: `{filters.search}``
                </span>
              )}
              {filters.status && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Trạng thái: {TEST_STATUS_OPTIONS.find(s => s.value === filters.status)?.label}
                </span>
              )}
              {filters.id_lesson && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                  Bài học: {lessonOptions.find(l => l.id === filters.id_lesson)?.title || 'N/A'}
                </span>
              )}
            </div>
          </div>
        )}

        {/* Quick Filter Buttons */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <p className="text-sm font-medium text-gray-700 mb-2">Lọc nhanh:</p>
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                onFiltersChange({ ...filters, status: 'active' });
                onSearch();
              }}
              disabled={isLoading}
              className="text-xs"
            >
              Đang hoạt động
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                onFiltersChange({ ...filters, status: 'inactive' });
                onSearch();
              }}
              disabled={isLoading}
              className="text-xs"
            >
              Không hoạt động
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // This would need additional logic to filter by current time
                onFiltersChange(filters);
                onSearch();
              }}
              disabled={isLoading}
              className="text-xs"
            >
              Đang diễn ra
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
