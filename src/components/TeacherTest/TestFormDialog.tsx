import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import {
  useForm,
  Controller,
  SubmitHandler,
} from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { FileText, Save, X, Clock, Calendar, HelpCircle } from "lucide-react";
import {
  TestFormData,
  TEST_STATUS_OPTIONS,
  TEST_CONFIG,
  LessonOption,
} from "@/types/test";
import { formatDateTimeForAPI, validateTestDates, formatDateTimeForForm } from "@/api/teacher/testAPI";
import { TestQuestionManager } from "./TestQuestionManager";

// Validation schema
const testSchema = z.object({
  title: z
    .string()
    .min(1, "Tiêu đề là bắt buộc")
    .max(255, "Tiêu đề không được quá 255 ký tự"),
  description: z.string().optional(),
  id_lesson: z.string().min(1, "Vui lòng chọn bài học"),
  time_test: z
    .number()
    .min(
      TEST_CONFIG.minDuration,
      `Thời gian tối thiểu ${TEST_CONFIG.minDuration} phút`
    )
    .max(
      TEST_CONFIG.maxDuration,
      `Thời gian tối đa ${TEST_CONFIG.maxDuration} phút`
    ),
  time_start: z.string().min(1, "Thời gian bắt đầu là bắt buộc"),
  time_end: z.string().min(1, "Thời gian kết thúc là bắt buộc"),
  status: z.enum(["active", "inactive"]),
  point_min: z
    .number()
    .min(
      TEST_CONFIG.minPassingScore,
      `Điểm tối thiểu ${TEST_CONFIG.minPassingScore}`
    )
    .max(
      TEST_CONFIG.maxPassingScore,
      `Điểm tối đa ${TEST_CONFIG.maxPassingScore}`
    ),
  questions: z
    .array(
      z.object({
        question: z
          .string()
          .min(1, "Câu hỏi là bắt buộc")
          .max(1000, "Câu hỏi không được quá 1000 ký tự"),
        option_a: z
          .string()
          .min(1, "Đáp án A là bắt buộc")
          .max(500, "Đáp án A không được quá 500 ký tự"),
        option_b: z
          .string()
          .min(1, "Đáp án B là bắt buộc")
          .max(500, "Đáp án B không được quá 500 ký tự"),
        option_c: z
          .string()
          .min(1, "Đáp án C là bắt buộc")
          .max(500, "Đáp án C không được quá 500 ký tự"),
        option_d: z
          .string()
          .min(1, "Đáp án D là bắt buộc")
          .max(500, "Đáp án D không được quá 500 ký tự"),
        option_true: z.enum(["A", "B", "C", "D"], {
          required_error: "Vui lòng chọn đáp án đúng",
        }),
      })
    )
    .min(
      TEST_CONFIG.minQuestions,
      `Tối thiểu ${TEST_CONFIG.minQuestions} câu hỏi`
    )
    .max(
      TEST_CONFIG.maxQuestions,
      `Tối đa ${TEST_CONFIG.maxQuestions} câu hỏi`
    ),
});

interface TestFormDialogProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  isEditing: boolean;
  onSubmit: SubmitHandler<TestFormData>;
  isSubmitting: boolean;
  initialData?: Partial<TestFormData>;
  testId?: string; // Add test ID for question management
  testTitle?: string; // Add test title for question management
  lessonOptions: LessonOption[];
}

export function TestFormDialog({
  isOpen,
  onOpenChange,
  isEditing,
  onSubmit,
  isSubmitting,
  initialData,
  testId,
  testTitle,
  lessonOptions,
}: TestFormDialogProps) {
  const [dateTimeError, setDateTimeError] = useState<string | null>(null);
  const [isQuestionManagerOpen, setIsQuestionManagerOpen] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<TestFormData>({
    resolver: zodResolver(testSchema),
    defaultValues: {
      title: "",
      description: "",
      id_lesson: "",
      time_test: 30,
      time_start: "",
      time_end: "",
      status: "active",
      point_min: 5,
      questions: [
        {
          question: "",
          option_a: "",
          option_b: "",
          option_c: "",
          option_d: "",
          option_true: "A",
        },
      ],
      ...initialData,
    },
  });



  const watchedStartTime = watch("time_start");
  const watchedEndTime = watch("time_end");

  // Validate date/time when they change
  useEffect(() => {
    if (watchedStartTime && watchedEndTime) {
      const startDate = new Date(watchedStartTime);
      const endDate = new Date(watchedEndTime);
      const error = validateTestDates(startDate, endDate);
      setDateTimeError(error);
    }
  }, [watchedStartTime, watchedEndTime]);

  useEffect(() => {
    if (isOpen && initialData) {
      reset({
        title: initialData.title || "",
        description: initialData.description || "",
        id_lesson: initialData.id_lesson || "",
        time_test: initialData.time_test || 30,
        time_start: initialData.time_start || "",
        time_end: initialData.time_end || "",
        status: initialData.status || "active",
        point_min: initialData.point_min || 5,
        questions: initialData.questions || [
          {
            question: "",
            option_a: "",
            option_b: "",
            option_c: "",
            option_d: "",
            option_true: "A",
          },
        ],
      });
    } else if (isOpen && !isEditing) {
      reset({
        title: "",
        description: "",
        id_lesson: "",
        time_test: 30,
        time_start: "",
        time_end: "",
        status: "active",
        point_min: 5,
        questions: [
          {
            question: "",
            option_a: "",
            option_b: "",
            option_c: "",
            option_d: "",
            option_true: "A",
          },
        ],
      });
    }
  }, [isOpen, initialData, isEditing, reset]);

  const handleFormSubmit: SubmitHandler<TestFormData> = (data) => {
    if (dateTimeError) {
      return;
    }
    onSubmit(data);
  };



  // Use the datetime formatting function from API
  const formatDateTimeLocal = formatDateTimeForForm;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="md:min-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {isEditing ? "Chỉnh sửa bài kiểm tra" : "Thêm bài kiểm tra mới"}
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? "Cập nhật thông tin bài kiểm tra và các câu hỏi."
              : "Tạo bài kiểm tra mới với thông tin chi tiết và câu hỏi."}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          <div className="grid gap-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Title */}
              <div className="space-y-2">
                <Label htmlFor="title">
                  Tiêu đề <span className="text-red-500">*</span>
                </Label>
                <Controller
                  name="title"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="title"
                      placeholder="Nhập tiêu đề bài kiểm tra..."
                      disabled={isSubmitting}
                    />
                  )}
                />
                {errors.title && (
                  <p className="text-sm text-red-600">{errors.title.message}</p>
                )}
              </div>

              {/* Lesson Selection */}
              <div className="space-y-2">
                <Label htmlFor="id_lesson">
                  Bài học <span className="text-red-500">*</span>
                </Label>
                <Controller
                  name="id_lesson"
                  control={control}
                  render={({ field }) => (
                    <Select
                      onValueChange={field.onChange}
                      value={String(field.value)}
                    >
                      <SelectTrigger className="w-full" id="id_lesson">
                        <SelectValue placeholder="Chọn bài học" />
                      </SelectTrigger>
                      <SelectContent>
                        {lessonOptions.map((lesson) => (
                          <SelectItem key={lesson.id} value={String(lesson.id)}>
                            {lesson.title}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.id_lesson && (
                  <p className="text-sm text-red-600">
                    {errors.id_lesson.message}
                  </p>
                )}
              </div>
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description">Mô tả</Label>
              <Controller
                name="description"
                control={control}
                render={({ field }) => (
                  <Textarea
                    {...field}
                    id="description"
                    placeholder="Nhập mô tả bài kiểm tra..."
                    className="min-h-[80px]"
                    disabled={isSubmitting}
                  />
                )}
              />
              {errors.description && (
                <p className="text-sm text-red-600">
                  {errors.description.message}
                </p>
              )}
            </div>

            {/* Test Configuration */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Duration */}
              <div className="space-y-2">
                <Label htmlFor="time_test" className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Thời gian (phút) <span className="text-red-500">*</span>
                </Label>
                <Controller
                  name="time_test"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="time_test"
                      type="number"
                      min={TEST_CONFIG.minDuration}
                      max={TEST_CONFIG.maxDuration}
                      onChange={(e) =>
                        field.onChange(parseInt(e.target.value) || 0)
                      }
                      disabled={isSubmitting}
                    />
                  )}
                />
                {errors.time_test && (
                  <p className="text-sm text-red-600">
                    {errors.time_test.message}
                  </p>
                )}
              </div>

              {/* Minimum Score */}
              <div className="space-y-2">
                <Label htmlFor="point_min">
                  Điểm tối thiểu <span className="text-red-500">*</span>
                </Label>
                <Controller
                  name="point_min"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="point_min"
                      type="number"
                      min={TEST_CONFIG.minPassingScore}
                      max={TEST_CONFIG.maxPassingScore}
                      step="0.1"
                      onChange={(e) =>
                        field.onChange(parseFloat(e.target.value) || 0)
                      }
                      disabled={isSubmitting}
                    />
                  )}
                />
                {errors.point_min && (
                  <p className="text-sm text-red-600">
                    {errors.point_min.message}
                  </p>
                )}
              </div>

              {/* Status */}
              <div className="space-y-2">
                <Label htmlFor="status">Trạng thái</Label>
                <Controller
                  name="status"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                      disabled={isSubmitting}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Chọn trạng thái" />
                      </SelectTrigger>
                      <SelectContent>
                        {TEST_STATUS_OPTIONS.map((status) => (
                          <SelectItem key={status.value} value={status.value}>
                            {status.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.status && (
                  <p className="text-sm text-red-600">
                    {errors.status.message}
                  </p>
                )}
              </div>
            </div>

            {/* Date/Time Configuration */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Start Time */}
              <div className="space-y-2">
                <Label htmlFor="time_start" className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Thời gian bắt đầu <span className="text-red-500">*</span>
                </Label>
                <Controller
                  name="time_start"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="time_start"
                      type="datetime-local"
                      value={formatDateTimeLocal(field.value)}
                      onChange={(e) => {
                        const date = new Date(e.target.value);
                        field.onChange(formatDateTimeForAPI(date));
                      }}
                      disabled={isSubmitting}
                    />
                  )}
                />
                {errors.time_start && (
                  <p className="text-sm text-red-600">
                    {errors.time_start.message}
                  </p>
                )}
              </div>

              {/* End Time */}
              <div className="space-y-2">
                <Label htmlFor="time_end" className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Thời gian kết thúc <span className="text-red-500">*</span>
                </Label>
                <Controller
                  name="time_end"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="time_end"
                      type="datetime-local"
                      value={formatDateTimeLocal(field.value)}
                      onChange={(e) => {
                        const date = new Date(e.target.value);
                        field.onChange(formatDateTimeForAPI(date));
                      }}
                      disabled={isSubmitting}
                    />
                  )}
                />
                {errors.time_end && (
                  <p className="text-sm text-red-600">
                    {errors.time_end.message}
                  </p>
                )}
              </div>
            </div>

            {/* Date/Time Validation Error */}
            {dateTimeError && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-600">{dateTimeError}</p>
              </div>
            )}

            {/* Questions Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <HelpCircle className="h-5 w-5" />
                  Câu hỏi
                </h3>
                {isEditing && testId && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsQuestionManagerOpen(true)}
                    disabled={isSubmitting}
                    className="flex items-center gap-2"
                  >
                    <HelpCircle className="h-4 w-4" />
                    Quản lý câu hỏi
                  </Button>
                )}
              </div>

              {!isEditing && (
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <strong>Lưu ý:</strong> Sau khi tạo bài kiểm tra, bạn có thể quản lý câu hỏi bằng cách chỉnh sửa bài kiểm tra và sử dụng chức năng `Quản lý câu hỏi`.
                  </p>
                </div>
              )}

              {isEditing ? (
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-sm text-green-800">
                    <strong>Quản lý câu hỏi:</strong> Sử dụng nút `Quản lý câu hỏi` ở trên để thêm, xóa hoặc chỉnh sửa câu hỏi cho bài kiểm tra này.
                  </p>
                </div>
              ) : (
                <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
                  <p className="text-sm text-amber-800">
                    <strong>Tạo bài kiểm tra:</strong> Sau khi tạo bài kiểm tra thành công, bạn có thể quản lý câu hỏi bằng cách chỉnh sửa bài kiểm tra.
                  </p>
                </div>
              )}
            </div>
          </div>

          <DialogFooter className="gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              <X className="h-4 w-4 mr-2" />
              Hủy
            </Button>
            <Button type="submit" disabled={isSubmitting || !!dateTimeError}>
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  {isEditing ? "Đang cập nhật..." : "Đang thêm..."}
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Save className="h-4 w-4" />
                  {isEditing ? "Cập nhật" : "Thêm mới"}
                </div>
              )}
            </Button>
          </DialogFooter>
        </form>

        {/* Test Question Manager */}
        {isEditing && testId && testTitle && (
          <TestQuestionManager
            isOpen={isQuestionManagerOpen}
            onOpenChange={setIsQuestionManagerOpen}
            testId={testId}
            testTitle={testTitle}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}
