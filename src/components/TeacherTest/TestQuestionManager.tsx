import React, { useState, useEffect, useCallback } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Search, 
  Plus, 
  Trash2, 
  HelpCircle, 
  CheckCircle, 
  Eye,
  RefreshCw,
  AlertCircle
} from "lucide-react";
import { toast } from "sonner";
import { getAuthToken } from "@/utils/getAuthToken";

// API imports
import {
  listQuestionsInTestAPI,
  addQuestionToTestAPI,
  deleteQuestionFromTestAPI,
  TestQuestionItem,
  formatQuestionForDisplay,
  getCorrectAnswerText,
} from "@/api/teacher/testQuestionAPI";
import { listQuestionsAPI } from "@/api/teacher/questionAPI";
import { Question } from "@/types/question";

interface TestQuestionManagerProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  testId: string;
  testTitle: string;
}

export function TestQuestionManager({
  isOpen,
  onOpenChange,
  testId,
  testTitle,
}: TestQuestionManagerProps) {
  // State for test questions
  const [testQuestions, setTestQuestions] = useState<TestQuestionItem[]>([]);
  const [isLoadingTestQuestions, setIsLoadingTestQuestions] = useState(false);
  const [testQuestionsPage, setTestQuestionsPage] = useState(1);
  const [testQuestionsTotalPages, setTestQuestionsTotalPages] = useState(1);
  const [testQuestionsSearch, setTestQuestionsSearch] = useState("");

  // State for available questions
  const [availableQuestions, setAvailableQuestions] = useState<Question[]>([]);
  const [isLoadingAvailableQuestions, setIsLoadingAvailableQuestions] = useState(false);
  const [availableQuestionsPage, setAvailableQuestionsPage] = useState(1);
  const [availableQuestionsTotalPages, setAvailableQuestionsTotalPages] = useState(1);
  const [availableQuestionsSearch, setAvailableQuestionsSearch] = useState("");

  // State for selections and operations
  const [selectedTestQuestions, setSelectedTestQuestions] = useState<string[]>([]);
  const [selectedAvailableQuestions, setSelectedAvailableQuestions] = useState<string[]>([]);
  const [isOperating, setIsOperating] = useState(false);

  // Load test questions
  const loadTestQuestions = useCallback(async (page: number = 1, search: string = "") => {
    const token = getAuthToken();
    if (!token) {
      toast.error("Không tìm thấy token xác thực");
      return;
    }

    setIsLoadingTestQuestions(true);
    try {
      const response = await listQuestionsInTestAPI(token, testId, search, page, 10);
      setTestQuestions(response.data);
      setTestQuestionsTotalPages(response.totalPage);
      setTestQuestionsPage(page);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi tải câu hỏi";
      toast.error(errorMessage);
    } finally {
      setIsLoadingTestQuestions(false);
    }
  }, [testId]);

  // Load available questions
  const loadAvailableQuestions = useCallback(async (page: number = 1, search: string = "") => {
    const token = getAuthToken();
    if (!token) {
      toast.error("Không tìm thấy token xác thực");
      return;
    }

    setIsLoadingAvailableQuestions(true);
    try {
      const filters: any = { status: 'active' };
      if (search) filters.search = search;

      const response = await listQuestionsAPI(filters, page, token, 10);
      setAvailableQuestions(response.data);
      setAvailableQuestionsTotalPages(response.totalPage || Math.ceil(response.totalData / 10));
      setAvailableQuestionsPage(page);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi tải danh sách câu hỏi";
      toast.error(errorMessage);
    } finally {
      setIsLoadingAvailableQuestions(false);
    }
  }, []);

  // Initial load
  useEffect(() => {
    if (isOpen && testId) {
      loadTestQuestions();
      loadAvailableQuestions();
    }
  }, [isOpen, testId, loadTestQuestions, loadAvailableQuestions]);

  // Handle search for test questions
  const handleTestQuestionsSearch = () => {
    loadTestQuestions(1, testQuestionsSearch);
  };

  // Handle search for available questions
  const handleAvailableQuestionsSearch = () => {
    loadAvailableQuestions(1, availableQuestionsSearch);
  };

  // Handle add questions to test
  const handleAddQuestionsToTest = async () => {
    if (selectedAvailableQuestions.length === 0) {
      toast.error("Vui lòng chọn ít nhất một câu hỏi để thêm");
      return;
    }

    const token = getAuthToken();
    if (!token) {
      toast.error("Không tìm thấy token xác thực");
      return;
    }

    setIsOperating(true);
    try {
      await addQuestionToTestAPI({
        token,
        id_test: testId,
        type: 'existing',
        id_questions: selectedAvailableQuestions,
      });

      toast.success(`Đã thêm ${selectedAvailableQuestions.length} câu hỏi vào bài kiểm tra`);
      setSelectedAvailableQuestions([]);
      
      // Reload test questions
      loadTestQuestions(testQuestionsPage, testQuestionsSearch);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi thêm câu hỏi";
      toast.error(errorMessage);
    } finally {
      setIsOperating(false);
    }
  };

  // Handle remove questions from test
  const handleRemoveQuestionsFromTest = async () => {
    if (selectedTestQuestions.length === 0) {
      toast.error("Vui lòng chọn ít nhất một câu hỏi để xóa");
      return;
    }

    const token = getAuthToken();
    if (!token) {
      toast.error("Không tìm thấy token xác thực");
      return;
    }

    setIsOperating(true);
    try {
      await deleteQuestionFromTestAPI({
        token,
        id_test: testId,
        id_questions: selectedTestQuestions,
      });

      toast.success(`Đã xóa ${selectedTestQuestions.length} câu hỏi khỏi bài kiểm tra`);
      setSelectedTestQuestions([]);
      
      // Reload test questions
      loadTestQuestions(testQuestionsPage, testQuestionsSearch);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi xóa câu hỏi";
      toast.error(errorMessage);
    } finally {
      setIsOperating(false);
    }
  };

  // Handle test question selection
  const handleTestQuestionSelect = (questionId: string, isSelected: boolean) => {
    if (isSelected) {
      setSelectedTestQuestions(prev => [...prev, questionId]);
    } else {
      setSelectedTestQuestions(prev => prev.filter(id => id !== questionId));
    }
  };

  // Handle available question selection
  const handleAvailableQuestionSelect = (questionId: string, isSelected: boolean) => {
    if (isSelected) {
      setSelectedAvailableQuestions(prev => [...prev, questionId]);
    } else {
      setSelectedAvailableQuestions(prev => prev.filter(id => id !== questionId));
    }
  };

  // Select all test questions
  const handleSelectAllTestQuestions = (isSelected: boolean) => {
    if (isSelected) {
      setSelectedTestQuestions(testQuestions.map(q => q.id));
    } else {
      setSelectedTestQuestions([]);
    }
  };

  // Select all available questions
  const handleSelectAllAvailableQuestions = (isSelected: boolean) => {
    if (isSelected) {
      setSelectedAvailableQuestions(availableQuestions.map(q => q.id));
    } else {
      setSelectedAvailableQuestions([]);
    }
  };

  const truncateText = (text: string, maxLength: number = 80) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="md:min-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <HelpCircle className="h-5 w-5" />
            Quản lý câu hỏi - {testTitle}
          </DialogTitle>
          <DialogDescription>
            Quản lý câu hỏi cho bài kiểm tra. Bạn có thể thêm câu hỏi từ ngân hàng câu hỏi hoặc xóa câu hỏi khỏi bài kiểm tra.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="test-questions" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="test-questions" className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Câu hỏi trong bài kiểm tra ({testQuestions.length})
            </TabsTrigger>
            <TabsTrigger value="add-questions" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Thêm câu hỏi
            </TabsTrigger>
          </TabsList>

          {/* Test Questions Tab */}
          <TabsContent value="test-questions" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Câu hỏi trong bài kiểm tra</span>
                  <div className="flex items-center gap-2">
                    {selectedTestQuestions.length > 0 && (
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={handleRemoveQuestionsFromTest}
                        disabled={isOperating}
                        className="flex items-center gap-2"
                      >
                        <Trash2 className="h-4 w-4" />
                        Xóa đã chọn ({selectedTestQuestions.length})
                      </Button>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => loadTestQuestions(testQuestionsPage, testQuestionsSearch)}
                      disabled={isLoadingTestQuestions}
                    >
                      <RefreshCw className="h-4 w-4" />
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Search */}
                <div className="flex gap-2">
                  <Input
                    placeholder="Tìm kiếm câu hỏi..."
                    value={testQuestionsSearch}
                    onChange={(e) => setTestQuestionsSearch(e.target.value)}
                    onKeyDown={(e) => e.key === "Enter" && handleTestQuestionsSearch()}
                    disabled={isLoadingTestQuestions}
                  />
                  <Button onClick={handleTestQuestionsSearch} disabled={isLoadingTestQuestions}>
                    <Search className="h-4 w-4" />
                  </Button>
                </div>

                {/* Questions Table */}
                {isLoadingTestQuestions ? (
                  <div className="flex justify-center items-center h-32">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : testQuestions.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <AlertCircle className="mx-auto h-12 w-12 mb-4" />
                    <p>Chưa có câu hỏi nào trong bài kiểm tra này</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="w-12">
                              <Checkbox
                                checked={selectedTestQuestions.length === testQuestions.length && testQuestions.length > 0}
                                onCheckedChange={handleSelectAllTestQuestions}
                                disabled={isOperating}
                              />
                            </TableHead>
                            <TableHead>Câu hỏi</TableHead>
                            <TableHead className="w-32">Đáp án đúng</TableHead>
                            <TableHead className="w-24">Trạng thái</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {testQuestions.map((question) => (
                            <TableRow key={question.id}>
                              <TableCell>
                                <Checkbox
                                  checked={selectedTestQuestions.includes(question.id)}
                                  onCheckedChange={(checked) => 
                                    handleTestQuestionSelect(question.id, checked as boolean)
                                  }
                                  disabled={isOperating}
                                />
                              </TableCell>
                              <TableCell>
                                <div className="space-y-2">
                                  <p className="font-medium">
                                    {truncateText(question.question)}
                                  </p>
                                  <div className="grid grid-cols-2 gap-1 text-xs text-gray-500">
                                    <div>A. {truncateText(question.option_a, 30)}</div>
                                    <div>B. {truncateText(question.option_b, 30)}</div>
                                    <div>C. {truncateText(question.option_c, 30)}</div>
                                    <div>D. {truncateText(question.option_d, 30)}</div>
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="space-y-1">
                                  <Badge variant="outline" className="font-mono">
                                    {question.option_true}
                                  </Badge>
                                  <p className="text-xs text-gray-500">
                                    {truncateText(getCorrectAnswerText(question), 20)}
                                  </p>
                                </div>
                              </TableCell>
                              <TableCell>
                                <Badge 
                                  variant={question.status === 'active' ? 'default' : 'secondary'}
                                  className={question.status === 'active' ? 'bg-green-100 text-green-800' : ''}
                                >
                                  {question.status === 'active' ? 'Hoạt động' : 'Không hoạt động'}
                                </Badge>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>

                    {/* Pagination for test questions */}
                    {testQuestionsTotalPages > 1 && (
                      <div className="flex justify-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => loadTestQuestions(testQuestionsPage - 1, testQuestionsSearch)}
                          disabled={testQuestionsPage <= 1 || isLoadingTestQuestions}
                        >
                          Trước
                        </Button>
                        <span className="flex items-center px-3 text-sm">
                          Trang {testQuestionsPage} / {testQuestionsTotalPages}
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => loadTestQuestions(testQuestionsPage + 1, testQuestionsSearch)}
                          disabled={testQuestionsPage >= testQuestionsTotalPages || isLoadingTestQuestions}
                        >
                          Sau
                        </Button>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Add Questions Tab */}
          <TabsContent value="add-questions" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Thêm câu hỏi từ ngân hàng câu hỏi</span>
                  <div className="flex items-center gap-2">
                    {selectedAvailableQuestions.length > 0 && (
                      <Button
                        onClick={handleAddQuestionsToTest}
                        disabled={isOperating}
                        className="flex items-center gap-2"
                      >
                        <Plus className="h-4 w-4" />
                        Thêm đã chọn ({selectedAvailableQuestions.length})
                      </Button>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => loadAvailableQuestions(availableQuestionsPage, availableQuestionsSearch)}
                      disabled={isLoadingAvailableQuestions}
                    >
                      <RefreshCw className="h-4 w-4" />
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Search */}
                <div className="flex gap-2">
                  <Input
                    placeholder="Tìm kiếm câu hỏi trong ngân hàng..."
                    value={availableQuestionsSearch}
                    onChange={(e) => setAvailableQuestionsSearch(e.target.value)}
                    onKeyDown={(e) => e.key === "Enter" && handleAvailableQuestionsSearch()}
                    disabled={isLoadingAvailableQuestions}
                  />
                  <Button onClick={handleAvailableQuestionsSearch} disabled={isLoadingAvailableQuestions}>
                    <Search className="h-4 w-4" />
                  </Button>
                </div>

                {/* Available Questions Table */}
                {isLoadingAvailableQuestions ? (
                  <div className="flex justify-center items-center h-32">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : availableQuestions.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <AlertCircle className="mx-auto h-12 w-12 mb-4" />
                    <p>Không tìm thấy câu hỏi nào</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="w-12">
                              <Checkbox
                                checked={selectedAvailableQuestions.length === availableQuestions.length && availableQuestions.length > 0}
                                onCheckedChange={handleSelectAllAvailableQuestions}
                                disabled={isOperating}
                              />
                            </TableHead>
                            <TableHead>Câu hỏi</TableHead>
                            <TableHead className="w-32">Đáp án đúng</TableHead>
                            <TableHead className="w-24">Trạng thái</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {availableQuestions.map((question) => {
                            // Check if question is already in test
                            const isInTest = testQuestions.some(tq => tq.id === question.id);

                            return (
                              <TableRow key={question.id} className={isInTest ? "bg-gray-50" : ""}>
                                <TableCell>
                                  <Checkbox
                                    checked={selectedAvailableQuestions.includes(question.id)}
                                    onCheckedChange={(checked) =>
                                      handleAvailableQuestionSelect(question.id, checked as boolean)
                                    }
                                    disabled={isOperating || isInTest}
                                  />
                                </TableCell>
                                <TableCell>
                                  <div className="space-y-2">
                                    <div className="flex items-center gap-2">
                                      <p className="font-medium">
                                        {truncateText(question.question)}
                                      </p>
                                      {isInTest && (
                                        <Badge variant="secondary" className="text-xs">
                                          Đã có trong bài kiểm tra
                                        </Badge>
                                      )}
                                    </div>
                                    <div className="grid grid-cols-2 gap-1 text-xs text-gray-500">
                                      <div>A. {truncateText(question.option_a, 30)}</div>
                                      <div>B. {truncateText(question.option_b, 30)}</div>
                                      <div>C. {truncateText(question.option_c, 30)}</div>
                                      <div>D. {truncateText(question.option_d, 30)}</div>
                                    </div>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <div className="space-y-1">
                                    <Badge variant="outline" className="font-mono">
                                      {question.option_true}
                                    </Badge>
                                    <p className="text-xs text-gray-500">
                                      {truncateText(
                                        question.option_true === 'A' ? question.option_a :
                                        question.option_true === 'B' ? question.option_b :
                                        question.option_true === 'C' ? question.option_c :
                                        question.option_d, 20
                                      )}
                                    </p>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <Badge
                                    variant={question.status === 'active' ? 'default' : 'secondary'}
                                    className={question.status === 'active' ? 'bg-green-100 text-green-800' : ''}
                                  >
                                    {question.status === 'active' ? 'Hoạt động' : 'Không hoạt động'}
                                  </Badge>
                                </TableCell>
                              </TableRow>
                            );
                          })}
                        </TableBody>
                      </Table>
                    </div>

                    {/* Pagination for available questions */}
                    {availableQuestionsTotalPages > 1 && (
                      <div className="flex justify-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => loadAvailableQuestions(availableQuestionsPage - 1, availableQuestionsSearch)}
                          disabled={availableQuestionsPage <= 1 || isLoadingAvailableQuestions}
                        >
                          Trước
                        </Button>
                        <span className="flex items-center px-3 text-sm">
                          Trang {availableQuestionsPage} / {availableQuestionsTotalPages}
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => loadAvailableQuestions(availableQuestionsPage + 1, availableQuestionsSearch)}
                          disabled={availableQuestionsPage >= availableQuestionsTotalPages || isLoadingAvailableQuestions}
                        >
                          Sau
                        </Button>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
