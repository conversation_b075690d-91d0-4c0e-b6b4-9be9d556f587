import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Eye, Edit, Trash2, FileText, Clock, Calendar } from "lucide-react";
import { Test, LessonOption } from "@/types/test";
import { formatDuration } from "@/api/teacher/testAPI";

interface TestTableProps {
  tests: Test[];
  isLoading: boolean;
  totalRecords: number;
  currentPage: number;
  itemsPerPage: number;
  lessonOptions: LessonOption[];
  onView: (test: Test) => void;
  onEdit: (test: Test) => void;
  onDelete: (id: string) => void;
}

export default function TestTable({
  tests,
  isLoading,
  totalRecords,
  currentPage,
  itemsPerPage,
  lessonOptions,
  onView,
  onEdit,
  onDelete,
}: TestTableProps) {
  const formatDateTime = (dateTimeString: string) => {
    try {
      const date = new Date(dateTimeString);
      return date.toLocaleDateString("vi-VN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch {
      return "N/A";
    }
  };

  const getLessonInfo = (id_lesson: string) => {
    return lessonOptions.find(lesson => lesson.id === id_lesson);
  };

  const getStatusBadge = (status: string) => {
    return status === "active" ? (
      <Badge variant="default" className="bg-green-100 text-green-800">
        Hoạt động
      </Badge>
    ) : (
      <Badge variant="secondary" className="bg-red-100 text-red-800">
        Không hoạt động
      </Badge>
    );
  };

  const truncateText = (text: string, maxLength: number = 50) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + "...";
  };

  const getTestStatus = (test: Test) => {
    const now = new Date();
    const startTime = new Date(test.time_start);
    const endTime = new Date(test.time_end);

    if (now < startTime) {
      return { status: 'upcoming', label: 'Sắp diễn ra', color: 'bg-blue-100 text-blue-800' };
    } else if (now >= startTime && now <= endTime) {
      return { status: 'ongoing', label: 'Đang diễn ra', color: 'bg-yellow-100 text-yellow-800' };
    } else {
      return { status: 'ended', label: 'Đã kết thúc', color: 'bg-gray-100 text-gray-800' };
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Danh sách bài kiểm tra
          </CardTitle>
          <CardDescription>
            Đang tải danh sách bài kiểm tra...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center items-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (tests.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Danh sách bài kiểm tra
          </CardTitle>
          <CardDescription>
            Không tìm thấy bài kiểm tra nào
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <FileText className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              Không có bài kiểm tra
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              Chưa có bài kiểm tra nào được tạo.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const startIndex = (currentPage - 1) * itemsPerPage + 1;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Danh sách bài kiểm tra
        </CardTitle>
        <CardDescription>
          Hiển thị {startIndex} - {Math.min(startIndex + itemsPerPage - 1, totalRecords)} 
          trong tổng số {totalRecords} bài kiểm tra
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">#</TableHead>
                <TableHead className="min-w-[200px]">Tiêu đề</TableHead>
                <TableHead className="min-w-[150px]">Bài học</TableHead>
                <TableHead className="w-24">Thời gian</TableHead>
                <TableHead className="w-40">Bắt đầu</TableHead>
                <TableHead className="w-40">Kết thúc</TableHead>
                <TableHead className="w-32">Trạng thái</TableHead>
                <TableHead className="w-24">Điểm tối thiểu</TableHead>
                <TableHead className="w-32">Thao tác</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {tests.map((test, index) => {
                const testStatus = getTestStatus(test);
                const lessonInfo = getLessonInfo(test.id_lesson);
                return (
                  <TableRow key={test.id}>
                    <TableCell className="font-medium">
                      {startIndex + index}
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <p className="font-medium">
                          {truncateText(test.title, 40)}
                        </p>
                        {test.description && (
                          <p className="text-xs text-gray-500">
                            {truncateText(test.description, 60)}
                          </p>
                        )}
                        <div className="flex items-center gap-1">
                          <Badge variant="outline" className={testStatus.color}>
                            {testStatus.label}
                          </Badge>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <p className="font-medium text-sm">
                          {lessonInfo?.title ? truncateText(lessonInfo.title, 30) : 'N/A'}
                        </p>
                        
                      </div>
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="flex items-center justify-center gap-1">
                        <Clock className="h-3 w-3 text-gray-400" />
                        <span className="text-sm font-mono">
                          {formatDuration(test.time_test)}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="text-sm">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3 text-gray-400" />
                        <span>{formatDateTime(test.time_start)}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-sm">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3 text-gray-400" />
                        <span>{formatDateTime(test.time_end)}</span>
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(test.status)}</TableCell>
                    <TableCell className="text-center">
                      <Badge variant="outline" className="font-mono">
                        {test.point_min}/10
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onView(test)}
                          className="h-8 w-8 p-0"
                          title="Xem chi tiết"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onEdit(test)}
                          className="h-8 w-8 p-0"
                          title="Chỉnh sửa"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onDelete(test.id)}
                          className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                          title="Xóa"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
