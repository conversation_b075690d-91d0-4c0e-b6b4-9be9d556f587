"use client";

import {
  QueryClient,
  QueryClientProvider,
  QueryClientConfig,
} from "@tanstack/react-query";
import { Provider as ReduxProvider } from "react-redux";
import { store } from "@/redux/store"; // Đường dẫn tới store của bạn
import { useEffect } from "react";
import {
  loadUserFromStorage,
  setUserAndToken,
} from "@/redux/features/user/userSlice";
import { getInfoMyMemberAPI } from "@/api/auth";
import { getAuthToken } from "@/utils/getAuthToken";
import Cookies from "universal-cookie";

// C<PERSON>u hình mặc định cho react-query (tùy chọn)
const queryClientOptions: QueryClientConfig = {
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 phút
      refetchOnWindowFocus: false, // Tắt refetch khi focus lại cửa sổ
    },
  },
};

// Khởi tạo queryClient một lần bên ngoài component để tránh tạo lại mỗi lần render
const queryClient = new QueryClient(queryClientOptions);

export function Providers({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    const loadUser = async () => {
      if (typeof window !== "undefined") {
        const token = getAuthToken();
        const cookies = new Cookies(); // Khởi tạo Cookies

        if (token) {
          try {
            const response = await getInfoMyMemberAPI(token);

            if (response.code === 1 && response.data) {
              store.dispatch(
                setUserAndToken({
                  user: response.data,
                  accessToken: response.data.token,
                })
              );
            } else {
              console.error(
                "Failed to fetch user info with token:",
                response.mess
              );
              // Tùy chọn: Xóa token nếu không hợp lệ
              // cookies.remove('accessToken', { path: '/' });
              // store.dispatch(clearUserAndToken()); // Giả sử bạn có action này
            }
          } catch (error) {
            console.error("Error fetching user info:", error);
            // Tùy chọn: Xóa token nếu gặp lỗi nghiêm trọng
            cookies.remove("accessTokenTeacher", { path: "/" });
         
            // store.dispatch(clearUserAndToken()); // Giả sử bạn có action này
          }
        }
        // Không cần else vì nếu không có token thì không làm gì cả
        // Hoặc bạn có thể dispatch một action để clear user state nếu cần
      }
    };

    loadUser();
  }, []);

  return (
    <ReduxProvider store={store}>
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    </ReduxProvider>
  );
}
