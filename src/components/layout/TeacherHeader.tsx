"use client";
import React from "react";

import { useRouter } from "next/navigation";
import { Menu, X, ChevronDown } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";

import Link from "next/link";
import { useSelector } from "react-redux";
import { RootState } from "./../../redux/store";
import { useDispatch } from "react-redux";
import { toast } from "sonner";
import { clearUserAndToken } from "@/redux/features/user/userSlice";
import Cookies from "universal-cookie";
import API from "@/lib/axios";

import { MdClass } from "react-icons/md";
import { RiCalendarScheduleLine } from "react-icons/ri";
import { IoDocumentText } from "react-icons/io5";
import { FaAddressBook } from "react-icons/fa6";
import { HiAcademicCap } from "react-icons/hi2";


export default function TeacherHeader() {
  const user_info = useSelector((state: RootState) => state.user.currentUser);
  const router = useRouter();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = React.useState(false);
  const dispatch = useDispatch();

  const handleLogout = async () => {
    const cookies = new Cookies();
    try {
      const token = user_info?.token || "";

      if (token) {
        const response = await API.post("/logoutMemberAPI", {
          token: token,
        });
        const data = response.data;

        switch (data.code) {
          case 1:
            toast.success(data.mess || "Đăng xuất thành công");
            break;
          case 3:
            toast.error("Tài khoản không tồn tại hoặc sai token");
            break;
          case 0:
            toast.error("Dữ liệu gửi kiểu POST");
            break;
          default:
            toast.error(data.mess || "Có lỗi xảy ra khi đăng xuất từ server");
            break;
        }
      } else {
        toast.success("Đăng xuất thành công (không có token phía client)");
      }
    } catch (error) {
      console.error("Lỗi khi gọi API đăng xuất:", error);
      toast.error("Có lỗi xảy ra khi kết nối máy chủ để đăng xuất.");
    } finally {
      cookies.remove("accessTokenTeacher", { path: "/" });
      dispatch(clearUserAndToken());
      router.push("/login");
    }
  };

  // Navigation items organized by category
  const navigationItems = [
    {
      label: "Lớp học của tôi",
      href: "/teacherClass",
      icon: MdClass,
    },
    {
      label: "Lịch giảng dạy",
      href: "/teacherSchedule",
      icon: RiCalendarScheduleLine,
    },
    {
      label: "Quản lý tài liệu",
      href: "/teacherFile",
      icon: IoDocumentText,
    },
    {
      label: "Quản lý điểm số",
      href: "/teacherGrade",
      icon: FaAddressBook,
    },
  ];

  const contentItems = [
    {
      label: "Bài học",
      href: "/teacherLesson",
    },
    {
      label: "Bài thi",
      href: "/teacherTest",
    },
    {
      label: "Câu hỏi",
      href: "/teacherQuestion",
    },
  ];

  return (
    <header className="bg-white border-b border-gray-200 sticky top-0 z-50">
      <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/teacher" className="flex items-center flex-shrink-0">
            <img
              src="/images/logo_phoneix.png"
              alt="School Logo"
              className="h-8 w-auto"
            />
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-1">
            {/* Direct Navigation Links */}
            {navigationItems.map((item) => {
              const IconComponent = item.icon;
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors"
                >
                  <IconComponent className="mr-2 h-4 w-4" />
                  {item.label}
                </Link>
              );
            })}

            {/* Content Management Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors">
                <HiAcademicCap className="mr-2 h-4 w-4" />
                Nội dung học tập
                <ChevronDown className="ml-1 h-3 w-3" />
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                {contentItems.map((item) => (
                  <DropdownMenuItem key={item.href} asChild>
                    <Link href={item.href} className="w-full">
                      {item.label}
                    </Link>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* User Profile Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger className="flex items-center space-x-2 px-2 py-1 rounded-md hover:bg-gray-50 transition-colors">
              <img
                src={user_info?.image || "https://www.gravatar.com/avatar/?d=mp"}
                alt="User avatar"
                className="w-8 h-8 rounded-full border border-gray-200"
              />
              <span className="hidden lg:block text-sm font-medium text-gray-700">
                {user_info?.name}
              </span>
              <ChevronDown className="h-3 w-3 text-gray-500" />
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem asChild>
                <Link href="/teacherProfile" className="w-full">
                  Thông tin cá nhân
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/teacherChangePassword" className="w-full">
                  Đổi mật khẩu
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onSelect={handleLogout} className="text-red-600">
                Đăng xuất
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden p-2 rounded-md hover:bg-gray-100 transition-colors"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            aria-label="Toggle mobile menu"
          >
            {isMobileMenuOpen ? (
              <X className="h-5 w-5" />
            ) : (
              <Menu className="h-5 w-5" />
            )}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t border-gray-200 bg-white">
            <div className="px-2 pt-2 pb-3 space-y-1">
              {/* Mobile Navigation Links */}
              {navigationItems.map((item) => {
                const IconComponent = item.icon;
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className="flex items-center px-3 py-2 text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <IconComponent className="mr-3 h-5 w-5" />
                    {item.label}
                  </Link>
                );
              })}

              {/* Mobile Content Items */}
              <div className="pt-2">
                <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                  Nội dung học tập
                </div>
                {contentItems.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className="block px-6 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {item.label}
                  </Link>
                ))}
              </div>
            </div>
          </div>
        )}
      </nav>
    </header>
  );
}
