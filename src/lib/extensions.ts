/**
 * Chuyển timestamp (tính bằng giây hoặc mili-giây) sang định dạng ngày.
 * @param inputTimestamp - Unix timestamp (giây hoặc mili-giây)
 * @param shortYear - true nếu muốn định dạng dd/mm/yy, false cho dd/mm/yyyy
 * @returns string ngày dạng dd/mm/yy hoặc dd/mm/yyyy
 */
export function formatDate(
  inputTimestamp: any,
  shortYear: boolean = false
): string {
  // Nếu là timestamp dạng giây (có 10 chữ số), nhân với 1000 để ra mili-giây
  const timestamp =
    inputTimestamp < 1e12 ? inputTimestamp * 1000 : inputTimestamp;
  const date = new Date(timestamp);

  const day = String(date.getDate()).padStart(2, "0");
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const year = shortYear
    ? String(date.getFullYear()).slice(-2)
    : String(date.getFullYear());

  return `${day}/${month}/${year}`;
}

export const formatVND = (value: number) =>
  new Intl.NumberFormat("vi-VN", { style: "currency", currency: "VND" }).format(
    value
  );
