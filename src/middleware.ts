import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import Cookies from "universal-cookie";

// Danh sách các đường dẫn không yêu cầu đăng nhập
export const publicPaths = [
  "/",
  "/login",
  "/ForgotPassword", // Thêm đường dẫn này
];

// Các đường dẫn API thường không cần bảo vệ ở đây (trừ khi API đó cũng cần check auth)
// Hoặc bạn có thể có logic riêng cho API routes nếu cần
const apiPathsPrefix = "/api/";

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Bạn có thể mở rộng danh sách này nếu có các thư mục tài nguyên tĩnh khác.
  if (pathname.startsWith("/images/") || pathname.startsWith("/icons/") || pathname.endsWith(".jpg") || pathname.endsWith(".png") || pathname.endsWith(".ico")) {
    return NextResponse.next();
  }

  // Lấy token từ cookies bằng universal-cookie
  const cookieHeader = request.headers.get("cookie");
  const cookies = new Cookies(cookieHeader);
  const accessToken = cookies.get("accessTokenTeacher");

  // MỚI: Nếu người dùng đã đăng nhập và truy cập trang login (root path), chuyển hướng đến /dashboardTeacher
 if (accessToken && pathname === "/") {
    const teacherUrl = new URL("/teacher", request.url);
    return NextResponse.redirect(teacherUrl);
  } 



  // Kiểm tra nếu đường dẫn là public path hoặc là API path
  // Cập nhật logic kiểm tra isPublicPath
  const isPublicPath = publicPaths.some((publicPathValue) => {
    if (publicPathValue === "/") {
      return pathname === publicPathValue; // So sánh chính xác cho trang gốc
    }
    // Đối với các public path khác (nếu có), bạn có thể giữ startsWith
    // nếu chúng có các route con cũng là public (ví dụ: /docs, /docs/api)
    return pathname.startsWith(publicPathValue);
  });

  const isApiPath = pathname.startsWith(apiPathsPrefix);

  // Nếu người dùng cố gắng truy cập vào một trang không phải public
  // và không phải API path mà chưa đăng nhập (không có accessToken)
  if (!isPublicPath && !isApiPath && !accessToken) {
    // Redirect về trang login

    if (pathname !== "/") {
      // Chỉ redirect nếu không phải đang cố vào trang login ('/')
      const rootLoginUrl = new URL("/", request.url);
      return NextResponse.redirect(rootLoginUrl);
    }
  }

  // Nếu người dùng đã đăng nhập và truy cập public path/API path, cho phép tiếp tục
  return NextResponse.next();
}

// Cấu hình Matcher:
// Middleware này sẽ chạy cho TẤT CẢ các route NGOẠI TRỪ:
// - Các route bắt đầu bằng /api
// - Các route của Next.js cho static files (_next/static)
// - Các route của Next.js cho image optimization (_next/image)
// - favicon.ico
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
  ],
};
