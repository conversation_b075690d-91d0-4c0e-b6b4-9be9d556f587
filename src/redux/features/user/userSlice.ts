import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { User } from '@/types/auth';

// Định nghĩa kiểu dữ liệu cho trạng thái slice người dùng
interface UserState {
  currentUser: User | null;
  accessToken: string | null;
  avatarUrl: string | null; // Thêm trường mới này
}

const initialState: UserState = {
  currentUser: null,
  accessToken: null,
  avatarUrl: null, // Khởi tạo giá trị ban đầu
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    // Action để thiết lập thông tin người dùng và token khi đăng nhập thành công
    setUserAndToken: (state, action: PayloadAction<{ user: User; accessToken: string }>) => {
      state.currentUser = action.payload.user;
      state.accessToken = action.payload.user.token;
      // Lưu accessToken vào localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('accessToken', action.payload.accessToken);
      }
    },
    // Action để xóa thông tin người dùng và token khi đăng xuất
    clearUserAndToken: (state) => {
      state.currentUser = null;
      state.accessToken = null;
      // Xóa accessToken khỏi localStorage
      if (typeof window !== 'undefined') {
        localStorage.removeItem('accessToken');
        // localStorage.removeItem('currentUser');
      }
    },
    // Action để tải token (và có thể cả user) từ localStorage khi khởi động ứng dụng
    loadUserFromStorage: (state) => {
      if (typeof window !== 'undefined') {
        const token = localStorage.getItem('accessToken');
        if (token) {
          state.accessToken = token;
        }
      }
    },
    updateAvatar: (state, action: PayloadAction<string>) => {
      state.avatarUrl = action.payload;
    },
  },
});

export const { setUserAndToken, clearUserAndToken, loadUserFromStorage, updateAvatar } = userSlice.actions;
export default userSlice.reducer;