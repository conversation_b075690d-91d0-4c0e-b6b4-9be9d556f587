export interface Category {
  id: string;
  code?: string;
  fee?: number;
  name: string;
  parent: string;
  description: string;
  image: string;
  keyword: string;
  status: string;
  type: string;
  weighty: number;
}

export interface CategoryFilter {
  name?: string;
}

export interface CategoryFormData {
  id?: string;
  code: string;
  fee?: string;
  name: string;
  parent?: string;
  description?: string;
  keyword?: string;
  credit?: string;
}

export interface AcademicYear {
  id: number;
  name: string;
  code: string;
  id_parent: number;
  start_date: string; // ISO date string (e.g., "2024-10-01")
  end_date: string;   // ISO date string (e.g., "2025-08-01")
}

export interface Bank {
  id: string;
  name: string;
  code: string;
}
