export interface Major {
    id: string;
    name: string;
  }
  
  export interface Registration {
    id: string; 
    full_name: string;
    phone: string;
    email?: string;
    id_major: string;
    major_name?: string; // For display convenience
    registration_date: string; // ISO date string
    status: 'new' | 'done' | 'not_contact' | 'refuse';
    status_pay: 'new' | 'done';
    major?: Major[];
    created_at?: string; // ISO date string
    ethnicity?: string;
    gender: 'male' | 'female' | 'other' | '';
    birthplace?: string;
    id_card_number?: string;
    address_receive_admission?: string;
    code_student?: string;
    // Add any other relevant fields from your API
  }
  
  export interface RegistrationFormData {
    full_name: string;
    phone: string;
    email?: string;
    id_major: string[];
    status: 'new' | 'done' | 'not_contact' | 'refuse';
    status_pay: 'new' | 'done';
    ethnicity?: string;
    gender:  'male' | 'female' | 'other';
    birthplace?: string;
    id_card_number?: string;
    address_receive_admission?: string;
    code_student?: string;
  }
  
  export interface RegistrationFilters {
    phone?: string;
    status?: 'new' | 'done' | 'not_contact' | 'refuse' | '';
    status_pay?: 'new' | 'done' | '';
    full_name?: string;
    date_start?: string;
    date_end?: string;
    id_major?: string;
  }
  
  export interface PaginatedResponse<T> {
    data: T[];
    totalData: number;
  }