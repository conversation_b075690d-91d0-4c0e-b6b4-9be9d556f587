// Lesson Management Types
export interface Lesson {
  id: string;
  title: string;
  content: string;
  description?: string;
  youtube_code?: string;
  link_file_video?: string;
  id_course: string;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
  course_info?: {
    id: string;
    name: string;
    code: string;
    subject_info?: {
      id: string;
      name: string;
      keyword: string;
    };
  };
}

// Filter interface for lesson list
export interface LessonFilter {
  search?: string;
  status?: 'active' | 'inactive' | '';
  id_course?: string;
  faculty_name?: string;
  major_name?: string;
  subject_name?: string;
}

// Form data interface for creating/editing lessons
export interface LessonFormData {
  title: string;
  content: string;
  description?: string;
  youtube_code?: string;
  link_file_video?: File | string;
  id_course: string;
  status: 'active' | 'inactive';
}

// API Response Types
export interface LessonApiResponse<T> {
  code: number;
  mess: string;
  data: T;
  totalData?: {
    totalData: number;
    totalPage: number;
  };
}

export interface PaginatedLessonResponse {
  data: Lesson[];
  totalData: number;
  totalPage?: number;
}

// API Request Types
export interface ListLessonsRequest {
  token: string;
  search?: string;
  limit?: number;
  page?: number;
  status?: 'active' | 'inactive';
  id_course?: string;
  faculty_name?: string;
  major_name?: string;
  subject_name?: string;
}

export interface LessonDetailRequest {
  token: string;
  id: string;
}

export interface AddLessonRequest {
  token: string;
  title: string;
  content: string;
  description?: string;
  youtube_code?: string;
  link_file_video?: File | string;
  id_course: string;
  status: 'active' | 'inactive';
}

export interface DeleteLessonRequest {
  token: string;
  id: string;
}

// Course options for dropdown
export interface CourseOption {
  id: string;
  name: string;
  code: string;
  subject_info?: {
    id: string;
    name: string;
    keyword: string;
  };
}

// Status options
export const LESSON_STATUS_OPTIONS = [
  { value: 'active', label: 'Hoạt động' },
  { value: 'inactive', label: 'Không hoạt động' },
] as const;

// Video file validation
export interface VideoFileValidation {
  maxSize: number; // in bytes
  allowedTypes: string[];
  allowedExtensions: string[];
}

export const VIDEO_FILE_CONFIG: VideoFileValidation = {
  maxSize: 100 * 1024 * 1024, // 100MB
  allowedTypes: ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv'],
  allowedExtensions: ['.mp4', '.avi', '.mov', '.wmv', '.flv'],
};

// Form validation schema types
export interface LessonFormErrors {
  title?: string;
  content?: string;
  description?: string;
  youtube_code?: string;
  link_file_video?: string;
  id_course?: string;
  status?: string;
}

// Table column configuration
export interface LessonTableColumn {
  key: keyof Lesson | 'actions' | 'index';
  label: string;
  sortable?: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
}

export const LESSON_TABLE_COLUMNS: LessonTableColumn[] = [
  { key: 'index', label: 'STT', width: '60px', align: 'center' },
  { key: 'title', label: 'Tiêu đề', sortable: true },
  { key: 'course_info', label: 'Khóa học', width: '200px' },
  { key: 'status', label: 'Trạng thái', width: '120px', align: 'center' },
  { key: 'created_at', label: 'Ngày tạo', width: '150px', align: 'center' },
  { key: 'actions', label: 'Hành động', width: '200px', align: 'right' },
];

// Pagination configuration
export interface LessonPaginationConfig {
  defaultPage: number;
  defaultLimit: number;
  limitOptions: number[];
}

export const LESSON_PAGINATION_CONFIG: LessonPaginationConfig = {
  defaultPage: 1,
  defaultLimit: 20,
  limitOptions: [10, 20, 50, 100],
};
