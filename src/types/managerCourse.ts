export interface Course {
  id?: string;
  name: string;
  code: string;
  groups: string;
  id_teacher: number;
  id_semester: number;
  id_subject: number;
  fee: number;
  number_students: number;
  slug: string;
  created_at: string;
  updated_at: string;
  schedule_sessions: ScheduleSession[];
}

export interface CourseFilter {
  name?: string;
  code?: string;
}

export interface ScheduleSession {
  id?: number;
  id_course?: number;
  date_of_week: number; // Thứ trong tuần (2 = Th<PERSON>, 7 = Chủ Nhật)
  week_start: string; // Định dạng ngày: YYYY-MM-DD
  week_end: string;
  period_start: string; // <PERSON><PERSON> thể là giờ hoặc tiết học => kiểu string
  period_end: string;
  room: string;
}

export interface CourseFormData {
  id?: number;
  name: string;
  code: string;
  group: string;
  id_teacher: number | null;
  id_semester: number | null;
  id_subject: number | null;
  fee: string;
  number_students: string;
  schedule_sessions: ScheduleSession[];
}
