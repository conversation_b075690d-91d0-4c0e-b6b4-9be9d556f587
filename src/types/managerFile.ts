export interface SubjectItem {
  id: string;
  name: string;
  parent: number;
  image: string | null;
  keyword: string;
  description: string;
  type: string;
  slug: string;
  status: string | null;
  weighty: number;

}

export interface DocumentItem {
  id: string;
  name: string;
  link_file: string;
  location: string;
  note: string | null;
  status: string;
  id_subject: string;
  id_member: string;
  type: string;
  created_at: number;
  created_at_format: string;
  subject_info: SubjectItem;
}

export interface DocumentFilter {
    name?: string;
    id_subject?: string;
}

export interface DocumentFormData {
    name: string;
    id_subject: string;
     link_file: File | string | null;
    note?: string;
}
