export interface Source {
    id: string;
    name: string;
    phone: string;
    address: string;
    email: string;
    note: string;
    created_at: string | null;
    latitude: string;
    longitude: string;
    password: string;
    code_otp: string | null;
    token: string | null;
    status: 'active' | 'lock';
    token_device: string | null;
    last_login: string | null;
    image: string;
    percent_rose: number;
    bank_number: string;
    bank_code: string;
  }

  export interface SourceFilter{
    name?: string
  }
  
  export interface SourceFormData {
    name: string;
    phone: string;
    address?: string;
    email?: string;
    note?: string;
    latitude?: string;
    longitude?: string;
    password?: string;
    status?: 'active' | 'lock';
    image?: File | string | null;
    percent_rose?: number;
    bank_number? : string;
    bank_code? : string;
  }