interface Position {
    id: string;
    name: string;
    parent: number;
    image: string | null;
    keyword: string | null;
    description: string[]; // <PERSON><PERSON><PERSON><PERSON> sang array nếu parse JSON
    type: string;
    slug: string;
    status: string;
    weighty: number;
}

export interface Staff {
    id: string;
    full_name: string;
    name: string;
    avatar: string;
    phone: string;
    email: string;
    password: string;
    status: 'active' | 'lock';
    created_at: number;
    code_otp: number;
    address: string;
    token: string;
    token_device: string | null;
    birthday: string;
    birthplace: string;
    ethnicity: string;
    id_card_number: string;
    last_login: number;
    type: string | null;
    permission: string[]; // Đã parse JSON
    id_position: string;
    gender: string;
    position: Position;
}

export interface StaffFilters {
    phone?: string;
    status?: 'active' | 'lock' | '';
    full_name?: string;
}

export interface StaffFormData {
    avatar?: File | string | null;
    full_name: string;
    phone: string;
    email?: string;
    address?: string;
    status: 'active' | 'lock';
    ethnicity?: string;
    gender:'male' | 'female' | 'other';
    birthday?: string;
    birthplace?: string;
    id_card_number?: string;
    id_position: string;
    password: string;
}