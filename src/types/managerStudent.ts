import { Major } from "./courseRegistration";

export interface Student {
    id: string; 
    avatar?: string;
    full_name: string;
    phone: string;
    email?: string;
    address?: string;
    id_major: string;
    status: 'active' | 'lock';
    major?: Major[];
    created_at?: string; // ISO date string
    ethnicity?: string;
    gender: 'male' | 'female' | 'other' | '';
    birthplace?: string;
    id_card_number?: string;
    code_student?: string;
  }

  export interface StudentFormData {
    avatar?: string;
    full_name: string;
    phone: string;
    email?: string;
    address?: string;
    status: 'active' | 'lock';
    ethnicity?: string;
    gender: 'male' | 'female' | 'other' | '';
    birthday?: string;
    birthplace?: string;
    id_card_number?: string;
    code_student?: string;
    note?: string;
    password?: string;
  }

  export interface StudentFilters {
    phone?: string;
    status?: 'active' | 'lock' | '';
    full_name?: string;
    id_major?: string;
    code_student?: string;
  }

  export interface InfoFileStudentFilters{
    name?: string;
  }

  export interface InfoFileStudentData{
    id: string;
    name: string;
    type: string;
    id_student: number;
    id_member: number;
    id_subject: number;
    link_file: string;
    location: string | null;
    created_at: string;
    status: string;
    note: string;
    student: Student;
  }

  export interface InfoFileStudentFormData{
    name: string;
    link_file: File | string | null;
    type: 'info_profile' | 'point' | 'document';
    note?: string;
  }