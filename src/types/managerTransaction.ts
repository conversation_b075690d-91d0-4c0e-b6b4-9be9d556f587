export interface StudentTransaction {
    id: string;
    full_name: string;
    name: string;
    code_student: string | null;
    phone: string;
    address: string;
    email: string;
    status: string;
    created_at: number;
    birthday: string;
    birthplace: string;
    gender: string;
    ethnicity: string;
    id_card_number: string;
    money_paid: number;
    total_tuition: number;
    date_start: string | null;
    date_end: string | null;
    password: string;
    avatar: string | null;
    code_otp: string | null;
    last_login: string | null;
    token: string | null;
    token_device: string | null;
    id_major: string;
    note: string;
    id_source: string | null;
}

export interface Transaction {
    id: string;
    id_source: string;
    id_bill: string;
    id_student: string;
    money_total: number;
    money_back: number;
    create_at: number;
    status: string;
    percent: number;
    student: StudentTransaction;
}

export interface TransactionFilters {
    date_start?: string;
    date_end?: string;
}