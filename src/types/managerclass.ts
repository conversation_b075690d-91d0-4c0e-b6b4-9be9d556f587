
export interface Student {
    id: string;
    full_name: string;
    phone: string;
    code_student: string;
  }

  export interface StudentFilter {
    student_name?: string;
    student_code?: string;
  }
  
  export interface ClassRoom {
    id: string;
    name: string;
    subject: string;
    teacher: string;
    students: Student[];
    major: {
      id: string;
      name: string;
    };
    academic: {
      id: string;
      name: string;
    }
    createdAt: Date;
    semester: string;
    academicYear: string;
    id_academic?: string;
    id_major?: string;
    status?: 'active' | 'lock';
    id_member?: string;
  }

  export interface ClassFilter {
    name?: string;
    id_academic? : string;
    id_major?: string;
  }
  
  export interface ClassFormData {
    name: string;
    subject: string;
    teacher: string;
    semester: string;
    academicYear: string;
    id_academic?: string;
    id_major?: string;
    status?: 'active' | 'lock';
    id_member?: string;
  }
  
  export interface StudentFormData {
    name: string;
    email: string;
    studentId: string;
  }
  
  // API Response Types
  export interface ApiResponse<T> {
    code: number;
    mess: string;
    data: T;
  }
  
  export interface PaginatedResponse<T> {
    data: T[];
    total: number;
    page: number;
    limit: number;
  }
  
  // API Request Types
  export interface ListClassRequest {
    limit?: number;
    page?: number;
    id?: string;
    name?: string;
    token: string;
  }
  
  export interface CreateClassRequest {
    token: string;
    id?: string;
    name: string;
    id_academic: string;
    id_major: string;
    status: 'active' | 'lock';
    id_member: string;
  }
  
  export interface DetailClassRequest {
    token: string;
    id: string;
  }
  
  export interface ListStudentsRequest {
    token: string;
    id_class: string;
    page?: number;
    limit?: number;
  }
  
  export interface AddStudentsRequest {
    token: string;
    id_class: string;
    id_students: string[];
  }
  