export interface PositionMember {
  id: string;
  name: string;
  parent: number;
  image: string | null;
  keyword: string | null;
  description: string;
  type: "category_position";
  slug: string;
  status: "active" | "inactive" | string;
  weighty: number;
  permission: string[];
}

export interface PositionMemberFilter {
  name?: string;
}

export interface PermissionItem {
  name: string;
  permission: string;
}

export interface PermissionGroup {
  name: string;
  sub: PermissionItem[];
}

export interface PositionMemberFormData {
  name: string,
  permission: string[],
}
