// Question types for the question management system

export interface Question {
  id: string;
  question: string;
  option_a: string;
  option_b: string;
  option_c: string;
  option_d: string;
  option_true: 'A' | 'B' | 'C' | 'D';
  status: 'active' | 'inactive';
  question_sets?: QuestionSet[];
}

export interface QuestionSet {
  id: string;
  name: string;
  description?: string;
}

// Form data interface for creating/editing questions
export interface QuestionFormData {
  question: string;
  option_a: string;
  option_b: string;
  option_c: string;
  option_d: string;
  option_true: 'A' | 'B' | 'C' | 'D';
  status: 'active' | 'inactive';
}

// API Response Types
export interface QuestionApiResponse<T> {
  code: number;
  mess: string;
  data: T;
  totalData?: {
    totalData: number;
    totalPage: number;
  };
}

export interface PaginatedQuestionResponse {
  data: Question[];
  totalData: number;
  totalPage?: number;
}

// API Request Types
export interface ListQuestionsRequest {
  token: string;
  set_id?: number;
  exclude_set_id?: number;
  search?: string;
  page?: number;
  limit?: number;
}

export interface QuestionDetailRequest {
  token: string;
  id: string;
}

export interface AddQuestionRequest {
  token: string;
  question: string;
  option_a: string;
  option_b: string;
  option_c: string;
  option_d: string;
  option_true: 'A' | 'B' | 'C' | 'D';
  status: 'active' | 'inactive';
  id?: string; // For editing
}

export interface DeleteQuestionRequest {
  token: string;
  id: number;
  force_delete: boolean;
}

// Filter types
export interface QuestionFilter {
  search?: string;
  set_id?: number;
  exclude_set_id?: number;
  status?: 'active' | 'inactive';
}

// UI State types
export interface QuestionTableState {
  questions: Question[];
  isLoading: boolean;
  error: string | null;
  currentPage: number;
  totalPages: number;
  totalRecords: number;
  filters: QuestionFilter;
}

export interface QuestionFormState {
  isOpen: boolean;
  isEdit: boolean;
  editingQuestion: Question | null;
  isSubmitting: boolean;
}

export interface QuestionDetailState {
  isOpen: boolean;
  selectedQuestion: Question | null;
  isLoading: boolean;
}

// Constants
export const QUESTION_STATUS_OPTIONS = [
  { value: 'active', label: 'Hoạt động' },
  { value: 'inactive', label: 'Không hoạt động' }
] as const;

export const ANSWER_OPTIONS = [
  { value: 'A', label: 'A' },
  { value: 'B', label: 'B' },
  { value: 'C', label: 'C' },
  { value: 'D', label: 'D' }
] as const;
