//student page.tsx
export interface DashboardCardProps {
    title: string;
    description: string;
    icon: React.ElementType;
    linkTo: string;
    content: React.ReactNode;
  }

  export  interface ScheduleItemProps {
    time: string;
    subject: string;
    location: string;
  }

  export interface CourseItemProps {
    name: string;
    progress: number;
  }

  export interface GradeItemProps {
    subject: string;
    type: string;
    score: number;
  }

  export interface NotificationItemProps {
    title: string;
    time: string;
    isNew?: boolean;
  }

  export interface SurveyItemProps {
    title: string;
    deadline: string;
  }

  //course.tsx
  export interface CourseProps {
    course: {
      id: string;
      name: string;
      department: string;
      credits: number;
      schedule: string;
      students: number;
      teacher: string;
      room: string;
      status: string;
    };
  }

export interface NotificationProps {
  notification: {
    id: number;
    title: string;
    message: string;
    time: string;
    type: string;
    read: boolean;
  };
  markAsRead: (id: number) => void;
  getNotificationIcon: (type: string) => React.ReactElement;
}

  