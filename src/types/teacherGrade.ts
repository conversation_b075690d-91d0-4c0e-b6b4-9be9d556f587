export interface GradeDetail {
  score: number;
  weight: number;
  note: string;
  updated_at: string;
  grade_update_reason: string;
}

export interface Student {
  id: string;
  name: string;
  studentId: string;
  midterm_grade: number | GradeDetail | null;
  final_grade: number | GradeDetail | null;
  attendance: number | GradeDetail | null;
  practical_grade: number | GradeDetail | null;
  other_grade: number | GradeDetail | null;
  average: number;
  note_midterm_grade?: string;
  note_final_grade?: string;
  note_attendance?: string;
  note_practical_grade?: string;
  note_other_grade?: string;
  grade_update_reason_midterm?: string;
  grade_update_reason_finalterm?: string;
  grade_update_reason_attendance?: string;
  grade_update_reason_practical_grade?: string;
  grade_update_reason_other_grade?: string;
}

export interface StudentGrades {
  midterm_grade: number | GradeDetail | null;
  final_grade: number | GradeDetail | null;
  attendance: number | GradeDetail | null;
  practical_grade: number | GradeDetail | null;
  other_grade: number | GradeDetail | null;
}

export interface CourseInfo {
  id: number;
  name: string;
  code: string;
  groups: string;
  id_teacher: number;
  id_semester: number;
  id_subject: number;
  fee: number;
  number_students: number;
  slug: string;
}


export interface StudentInfo {
  id: number;
  full_name: string;
  email: string;
  phone: string;
  code_student: string | null;
  grades: StudentGrades | null;
  total_score: number;
  grade_letter: string;
}

export interface CourseData {
  course_info: CourseInfo;
  grade_types: string[];
  students: StudentInfo[];
}

// Tổng interface gói toàn bộ dữ liệu
export interface ApiResponse<T> {
  data: T;
  totalData: any;
}
