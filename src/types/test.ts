// Test types for the test management system

export interface TestQuestion {
  question: string;
  option_a: string;
  option_b: string;
  option_c: string;
  option_d: string;
  option_true: 'A' | 'B' | 'C' | 'D';
}

export interface Test {
  id: string;
  title: string;
  description?: string;
  id_lesson: string;
  time_test: number; // Duration in minutes
  time_start: string; // ISO datetime string
  time_end: string; // ISO datetime string
  status: 'active' | 'inactive';
  point_min: number; // Minimum passing score
  questions?: TestQuestion[];
  created_at?: string;
  updated_at?: string;
}

// Form data interface for creating/editing tests
export interface TestFormData {
  title: string;
  description?: string;
  id_lesson: string;
  time_test: number;
  time_start: string;
  time_end: string;
  status: 'active' | 'inactive';
  point_min: number;
  questions?: TestQuestion[];
}

// API Response Types
export interface TestApiResponse<T> {
  code: number;
  mess: string;
  data: T;
  totalData?: {
    page: number;
    limit: number;
    totalData: number;
    totalPage: number;
  };
}

export interface PaginatedTestResponse {
  data: Test[];
  totalData: number;
  totalPage?: number;
}

// API Request Types
export interface ListTestsRequest {
  token: string;
  limit?: number;
  page?: number;
  search?: string;
  status?: 'active' | 'inactive';
  id_lesson?: string;
}

export interface TestDetailRequest {
  token: string;
  id: string;
}

export interface AddTestRequest {
  token: string;
  title: string;
  description?: string;
  id_lesson: string;
  time_test: number;
  time_start: string;
  time_end: string;
  status: 'active' | 'inactive';
  point_min: number;
  questions?: TestQuestion[];
  id?: string; // For editing
}

export interface DeleteTestRequest {
  token: string;
  id: string;
}

// Filter types
export interface TestFilter {
  search?: string;
  status?: 'active' | 'inactive';
  id_lesson?: string;
}

// UI State types
export interface TestTableState {
  tests: Test[];
  isLoading: boolean;
  error: string | null;
  currentPage: number;
  totalPages: number;
  totalRecords: number;
  filters: TestFilter;
}

export interface TestFormState {
  isOpen: boolean;
  isEdit: boolean;
  editingTest: Test | null;
  isSubmitting: boolean;
}

export interface TestDetailState {
  isOpen: boolean;
  selectedTest: Test | null;
  isLoading: boolean;
}

// Lesson option for dropdown
export interface LessonOption {
  id: string;
  title: string;
}

// Constants
export const TEST_STATUS_OPTIONS = [
  { value: 'active', label: 'Hoạt động' },
  { value: 'inactive', label: 'Không hoạt động' }
] as const;

export const ANSWER_OPTIONS = [
  { value: 'A', label: 'A' },
  { value: 'B', label: 'B' },
  { value: 'C', label: 'C' },
  { value: 'D', label: 'D' }
] as const;

// Form validation types
export interface TestFormErrors {
  title?: string;
  description?: string;
  id_lesson?: string;
  time_test?: string;
  time_start?: string;
  time_end?: string;
  status?: string;
  point_min?: string;
  questions?: Array<{
    question?: string;
    option_a?: string;
    option_b?: string;
    option_c?: string;
    option_d?: string;
    option_true?: string;
  }>;
}

// Test configuration
export interface TestConfig {
  minDuration: number; // Minimum test duration in minutes
  maxDuration: number; // Maximum test duration in minutes
  minQuestions: number; // Minimum number of questions
  maxQuestions: number; // Maximum number of questions
  minPassingScore: number; // Minimum passing score
  maxPassingScore: number; // Maximum passing score
}

export const TEST_CONFIG: TestConfig = {
  minDuration: 5,
  maxDuration: 300, // 5 hours
  minQuestions: 1,
  maxQuestions: 100,
  minPassingScore: 0,
  maxPassingScore: 10,
};

// Table column configuration
export interface TestTableColumn {
  key: keyof Test | 'actions' | 'index';
  label: string;
  sortable?: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
}

export const TEST_TABLE_COLUMNS: TestTableColumn[] = [
  { key: 'index', label: 'STT', width: '60px', align: 'center' },
  { key: 'title', label: 'Tiêu đề', sortable: true },
  { key: 'time_test', label: 'Thời gian', width: '100px', align: 'center' },
  { key: 'time_start', label: 'Bắt đầu', width: '150px', align: 'center' },
  { key: 'time_end', label: 'Kết thúc', width: '150px', align: 'center' },
  { key: 'status', label: 'Trạng thái', width: '120px', align: 'center' },
  { key: 'point_min', label: 'Điểm tối thiểu', width: '120px', align: 'center' },
  { key: 'actions', label: 'Hành động', width: '200px', align: 'right' },
];

// Pagination configuration
export interface TestPaginationConfig {
  defaultPage: number;
  defaultLimit: number;
  limitOptions: number[];
}

export const TEST_PAGINATION_CONFIG: TestPaginationConfig = {
  defaultPage: 1,
  defaultLimit: 20,
  limitOptions: [10, 20, 50, 100],
};
