export interface AcademicYear {
    id: string | number;
    name: string;
    code?: string;
    id_parent?: number;
    start_date: string;
    end_date: string;
    status?: 'active' | 'inactive' | 'upcoming';
    description?: string;
    createdAt?: string;
    updatedAt?: string;
  }
  
  export interface CreateYearRequest {
    name: string;
    token?: string;
    code?: string;
    id_parent?: number;
    start_date: string;
    end_date: string;
    status?: 'active' | 'inactive' | 'upcoming';
    description?: string;
  }
  
  export interface UpdateYearRequest extends CreateYearRequest {
    id: string | number;
  }
  
  export interface ApiResponse<T> {
    data: T;
    code: number;
    mess: string;
    meta: any[];
    totalData: number;
  }
  