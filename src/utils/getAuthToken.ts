import Cookies from 'universal-cookie';

/**
 * L<PERSON>y accessToken từ cookie.
 * Hàm này có thể được sử dụng ở cả client-side và server-side (trong middleware hoặc API routes)
 * nếu context của cookie được truyền vào đúng cách.
 *
 * @param {string | object | null | undefined} [cookieHeaderOrRequest] - (<PERSON><PERSON><PERSON> chọ<PERSON>)
 *   - Client-side: Không cần truyền gì, Cookies sẽ tự động đọc từ document.cookie.
 *   - Server-side (Middleware/API Routes): Truyền request.headers.get('cookie') hoặc đối tượng request.
 * @returns {string | undefined} Giá trị của accessToken hoặc undefined nếu không tìm thấy.
 */
export const getAuthToken = (cookieHeaderOrRequest?: string | object | null | undefined): string | undefined => {
  let cookiesInstance;

  if (typeof window !== 'undefined') {
    // Client-side: universal-cookie sẽ tự động sử dụng document.cookie
    cookiesInstance = new Cookies(null, { path: '/' });
  } else if (cookieHeaderOrRequest) {
    // Server-side: cần truyền context của cookie
    // Nếu là string (header cookie):
    if (typeof cookieHeaderOrRequest === 'string') {
      cookiesInstance = new Cookies(cookieHeaderOrRequest, { path: '/' });
    }
    // Nếu là object (ví dụ NextRequest trong middleware):
    // Giả sử nó có thuộc tính 'cookies' là một instance của NextCookies hoặc tương tự
    // Hoặc nếu nó là chính request object và universal-cookie có thể xử lý
    else if (typeof cookieHeaderOrRequest === 'object' && cookieHeaderOrRequest !== null) {
        // Cố gắng lấy header cookie từ object request nếu có
        // Điều này phụ thuộc vào cấu trúc của object request bạn truyền vào
        const header = (cookieHeaderOrRequest as any).headers?.get?.('cookie');
        if (header) {
            cookiesInstance = new Cookies(header, { path: '/' });
        } else if ((cookieHeaderOrRequest as any).get?.('accessTokenTeacher')) {
            // Nếu object request đã là một instance của cookies hoặc có phương thức get
            return (cookieHeaderOrRequest as any).get('accessTokenTeacher');
        }
         else {
            // Nếu không xác định được header, thử khởi tạo trực tiếp
            // Điều này có thể không hoạt động đúng nếu object không phải là header string
            cookiesInstance = new Cookies(cookieHeaderOrRequest as any, { path: '/' });
        }
    } else {
        // Không có context cookie hợp lệ cho server-side
        return undefined;
    }
  } else {
    // Server-side nhưng không có context cookie được cung cấp
    return undefined;
  }

  if (!cookiesInstance) return undefined;

  return cookiesInstance.get('accessTokenTeacher');
};
