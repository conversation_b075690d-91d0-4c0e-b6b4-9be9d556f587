export const validateRequired = (value: string, fieldName: string): string => {
  return value.trim() ? "" : `${fieldName} is required`;
};

export const validateEmail = (email: string): string => {
  if (!email.trim()) return "";
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) ? "" : "Please enter a valid email address";
};

export const validatePhone = (phone: string): string => {
  if (!phone.trim()) return "";
  
  const phoneRegex = /^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/;
  return phoneRegex.test(phone) ? "" : "Please enter a valid phone number";
};

export const validateWebsite = (website: string): string => {
  if (!website.trim()) return "";
  
  try {
    const url = new URL(website);
    return url.protocol === "http:" || url.protocol === "https:" ? "" : "Please enter a valid website URL";
  } catch {
    return "Please enter a valid website URL";
  }
};

export const validateLogoFile = (file: File | null): string => {
  if (!file) return "";
  
  const validTypes = ['image/jpeg', 'image/png', 'image/gif'];
  const maxSize = 2 * 1024 * 1024; // 2MB
  
  if (!validTypes.includes(file.type)) {
    return "Logo must be JPG, PNG or GIF format";
  }
  
  if (file.size > maxSize) {
    return "Logo must be less than 2MB";
  }
  
  return "";
};
